<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>

  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />

  <link rel="icon" type="image/png" href="/favicon-48x48.png" sizes="48x48" />
  <link rel="shortcut icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="Allemans" />
  <link rel="manifest" href="/site.webmanifest" />

  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
</head>

<script async src="https://www.googletagmanager.com/gtag/js?id=G-EWXT780YLB"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-EWXT780YLB');
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Restaurant",
  "name": "<?php echo get_bloginfo('name'); ?>",
  "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
  "image": "<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>",
  "logo": "<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo'))); ?>",
  "url": "<?php echo esc_url(home_url()); ?>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>",
    "addressLocality": "Oostrum",
    "addressCountry": "NL"
  },
  "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "contactType": "customer service",
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>"
  },
  "sameAs": [
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
  ],
  "servesCuisine": "Bistro, Café",
  "acceptsReservations": "True",
  "reservationUrl": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-reserve-link')); ?>"
}
</script>

  <?php wp_head(); ?>
</head>
  <body class="no-scroll">
    <header id="header" class="scrolled" data-init data-delay=400>
      <div class="contentWrapper">
        <div class="col">
          <div class="logoWrapper">
            <a class="logo" title="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> Logo" href="/">
              <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> Logo">
              <img class="white" src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo-white'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> Logo" aria-hidden="true">
            </a>
          </div>
        </div>
        <div class="col">
          <a class="button primary outline" href="tel:<?php echo get_theme_mod('customTheme-main-callout-telephone'); ?>" title="<?php echo get_theme_mod('customTheme-main-callout-telephone-label'); ?>">
            <i class="icon-phone"></i>
            <span class="innerTextWrapper">
              <span class="absoluteText" aria-hidden="true"><?php echo get_theme_mod('customTheme-main-callout-telephone-label'); ?></span>
              <span class="absoluteText" aria-hidden="true"><?php echo get_theme_mod('customTheme-main-callout-telephone-label'); ?></span>
              <span class="innerText"><?php echo get_theme_mod('customTheme-main-callout-telephone-label'); ?></span>
            </span>
          </a>
          <a class="button black" data-no-swup href="<?php echo get_theme_mod('customTheme-main-callout-reserve-link'); ?>" title="<?php echo get_theme_mod('customTheme-main-callout-reserve-label'); ?>">
            <i class="icon-food"></i>
            <span class="innerTextWrapper">
              <span class="absoluteText" aria-hidden="true"><?php echo get_theme_mod('customTheme-main-callout-reserve-label'); ?></span>
              <span class="absoluteText" aria-hidden="true"><?php echo get_theme_mod('customTheme-main-callout-reserve-label'); ?></span>
              <span class="innerText"><?php echo get_theme_mod('customTheme-main-callout-reserve-label'); ?></span>
            </span>
          </a>
          <div class="hamburger"><span class="border"></span><span class="border"></span><span class="border"></span></div>
        </div>
      </div>
    </header>
    <div id="menu" data-lenis-prevent>
      <div class="background"><div class="col"></div><div class="col"></div><div class="bigLogoWrapper"><?php include("blocks/parts/big_logo.php");  ?></div></div>
      <div class="contentWrapper">
        <div class="cols">
          <div class="col top">
            <div class="innerCol">
              <nav class="primary-menu">
                  <?php wp_nav_menu(['theme_location' => 'primary-menu', 'container' => false, 'menu_class' => 'primaryMenu bigTitle']); ?>
              </nav>
            </div>
            <div class="innerCol blend">
              <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-menu-image'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> menu image">
            </div>
          </div>
          <div class="col">
            <div class="innerCol">
              <nav class="secondary-menu">
                  <?php wp_nav_menu(['theme_location' => 'secondary-menu', 'container' => false, 'menu_class' => 'secondaryMenu subTitle']); ?>
              </nav>
            </div>
            <div class="innerCol">
              <div class="contactDetails">
                <?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>
                <div class="links">
                  <a class="link" title="<?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="tel:<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank"><i class="icon-phone"></i><span><?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?></span></a>
                  <a class="link" title="Whatsapp: <?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="https://wa.me/<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank">
                      <i class="icon-whatsapp"></i><span><?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?></span>
                  </a>
                </div>
                <div class="socials">
                  <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-facebook')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a>
                  <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-instagram')); ?>" title="instagram" target="_blank"><i class="icon-instagram"></i></a>
                  <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-tiktok')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="pageContainer" class="transition-fade blocks">
