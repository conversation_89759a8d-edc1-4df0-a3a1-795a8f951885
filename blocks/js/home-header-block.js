var autoSlide;
var hammerDisabled = false;
$(document).ready(function(){
  $(document).on("initPage", function(){
    if ($(".homeHeaderBlock").length > 0) {
      initializeHomeHeaderBlock();
    }
  });
});

function setHomeHeaderBlock() {
  gsap.to(".homeHeaderBlock .backgroundSlides", {
    scale: 1.2,
    scrollTrigger: {
      trigger: ".homeHeaderBlock",
      start: "top-=150 top",
      end: "bottom top",
      scrub: true,
    },
  });
}


function initializeHomeHeaderBlock() {
  var currentIndex = 0;
  var slides = $(".homeHeaderBlock .backgroundSlides .slide");
  var totalSlides = slides.length;
  var direction = "next";

  setTimeout(function(){
    $(".homeHeaderBlock").addClass("active");
  });

  function startAutoSlide() {
    if (autoSlide) autoSlide.kill();
    autoSlide = gsap.timeline({ repeat: -1 });
    autoSlide.to({}, { duration: 4, onComplete: function () {
      nextSlide(currentIndex, totalSlides);
    }});
  }

  function nextSlide() {
    direction = "next";
    currentIndex = (currentIndex + 1) % totalSlides;
    showSlide(currentIndex, false);
    startAutoSlide();
    setTimeout(function(){
      hammerDisabled = false;
    }, 700)
  }
  

  function showSlide(index, first=false) {
    updateActiveTitleSlide(index, first);
    setTimeout(function(){
      hammerDisabled = false;
    }, 700)
  }


  function updateActiveTitleSlide(index, first) {
    if (index == totalSlides -1) {
      index = 0;
    } else if(first) {
      index = 0;
    } else {
      index = index + 1;
    }

    $(".homeHeaderBlock .backgroundSlides .slide").removeClass("active");
    $(".homeHeaderBlock .backgroundSlides .slide").eq(index).addClass("active");

  }

  $(".homeHeaderBlock .next").click(function () {
    nextSlide(currentIndex, totalSlides);
  });

  $(".homeHeaderBlock .prev").click(function () {
    direction = "prev";
    currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
    showSlide(currentIndex, false);
    startAutoSlide();
  });

  var slider = $(".homeHeaderBlock .backgroundSlidesWrapper")[0];
  var hammerReviewSlider = new Hammer(slider, { event: 'pan' });
  hammerReviewSlider.on('pan', function(e){
    if (!hammerDisabled) {
      if (e.additionalEvent == "panleft") {
        hammerDisabled = true;
        nextSlide(currentIndex, totalSlides);
      }
      if (e.additionalEvent == "panright") {
        hammerDisabled = true;
        direction = "prev";
        currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
        showSlide(currentIndex, false);
        startAutoSlide();
      }
    }
  });

  showSlide(currentIndex, true);
  startAutoSlide();
}

