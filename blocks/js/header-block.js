$(document).ready(function(){

  $(document).on("initPage", function(){
    initializeHeaderBlock();
  });
});

function initializeHeaderBlock() {
  if ($(".headerBlock").length > 0) {
    setHeaderBlock();
  }
}

function setHeaderBlock() {
  $(".headerBlock .headerButton").off("click.headerButton").on("click.headerButton", function() {
    scroller.scrollTo("section:nth-child(2)");
  });

  gsap.to(".headerBlock img", {
    scale: 1.2,
    scrollTrigger: {
      trigger: ".headerBlock",
      start: "top top",
      end: "bottom top",
      scrub: true,
    },
  });
}
