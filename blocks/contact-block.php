<?php
$image = get_field("image");
$size = 'large';
$video = get_field("video");
 ?>
<section class="contactBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <div class="imageWrapper">
          <div class="innerImage">
            <?php if ($video): ?>
              <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                <source src="<?php echo esc_url($video); ?>" type="video/mp4">
              </video>
            <?php elseif( $image ): ?>
              <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php endif; ?>          </div>
        </div>
      </div>
      <div class="col">
        <div class="textWrapper">
          <div class="subTitle primary">
            <?php echo esc_html(get_field("subtitle")); ?>
          </div>
          <h2 class="bigTitle splitThis" data-init data-split><?php the_field("title"); ?></h2>
          <div class="text">
            <?php echo get_field("text"); ?>
          </div>
        </div>
        <div class="formWrapper">
          <?php echo do_shortcode(get_field('form')); ?>
        </div>
      </div>
    </div>
  </div>
</section>
