.ctaBlock {
  &:last-of-type {
    padding-bottom: @vw100 + @vw50;
  }
  &.inview {
    .subTitle, .text, .imageWrapper, .buttonsWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      &.text {
          padding: 0 @vw112 + @vw16;
      }
      &.image {
        padding-right: @vw112 + @vw16;
      }
      &:not(.text) {
        &:first-child {
          padding-left: @vw112 + @vw16;
        }
      }
    }
  }
  .subTitle, .text, .imageWrapper, .buttonsWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    border-radius: @vw40;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .padding-bottom-generator(624, 359);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
  .buttonsWrapper {
    margin-top: @vw40;
    .button {
      &:not(:last-child){
          margin-right: @vw10;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .ctaBlock {
    &:last-of-type {
      padding-bottom: @vw100-1160 + @vw50-1160;
    }
    &.inview {
      .subTitle, .text, .imageWrapper, .buttonsWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .cols {
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);
      .col {
        display: inline-block;
        vertical-align: middle;
        margin: 0 @vw8-1160;
        width: calc(50% ~"-" @vw16-1160);
        &.text {
          padding: 0 @vw112-1160 + @vw16-1160;
          padding-left: 0;
        }
        &.image {
          padding-right: @vw112-1160 + @vw16-1160;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw112-1160 + @vw16-1160;
          }
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .text {
      margin-top: @vw30-1160;
    }
    .buttonsWrapper {
      margin-top: @vw40-1160;
      .button {
        &:not(:last-child) {
          margin-right: @vw10-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .ctaBlock {
    &:last-of-type {
      padding-bottom: @vw100-580 + @vw50-580;
      margin-bottom: 0;
    }
    &.inview {
      .subTitle, .text, .imageWrapper, .buttonsWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        display: inline-block;
        vertical-align: middle;
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &:not(:last-child) {
          margin-bottom: @vw40-580;
        }
        &.text {
          padding: 0;
        }
        &.image {
          padding-right: 0;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw112-580 + @vw16-580;
          }
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
    .buttonsWrapper {
      margin-top: @vw40-580;
      .button {
        &:not(:last-child) {
          margin-right: @vw10-580;
        }
      }
    }
  }
}
