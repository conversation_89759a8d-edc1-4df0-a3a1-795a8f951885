.headerBlock {
  position: relative;
  margin-top: 0;
  padding-top: 0 !important;
  padding-bottom: @vw100 + @vw30;
  text-align: center;
  .hugeTitle {
    display: block;
    width: 100%;
  }
  &.inview {
    .bigLogoWrapper {
      opacity: .05;
      .transitionMore(all, .3s, 1.2s);
    }
    .contentWrapper {
      .innerContent {
        .subTitle {
          opacity: 1;
          transform: translateY(0);
          .transitionMore(all, .45s, .9s, ease-in-out);
        }
      }
    }
    .headerButton {
      transform: translateY(50%) scale(1);
      .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
  }
  .hugeTitle {
    .wrapper {
      text-align: center !important;
    }
  }
  .bigLogoWrapper {
    position: absolute;
    width: (@vw112 * 6) + (@vw16 * 5);
    max-width: 100%;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    opacity: 0;
    z-index: -1;
    transform: translateY(50%) scale(1);
    pointer-events: none;
    will-change: transform;
    .svgWrapper {
      animation: rotate360 60s linear infinite;
    }
    svg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      path {
        fill: @hardBlack;
      }
    }
  }
  .background {
    overflow: hidden;
    border-radius: @vw20;
    position: absolute;
    top: 0;
    left: @vw20;
    width: calc(100% ~"-" @vw40);
    height: 100%;
    img {
      object-fit: cover;
      object-position: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      will-change: transform;
    }
    &:after {
      content: '';
      background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1));
      opacity: .6;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .contentWrapper {
    position: relative;
    .innerContent {
      padding: (@vw100 * 2.5) 0 @vw100 + @vw50;
      .titleWrapper {
        margin-bottom: @vw20;
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: flex-end;
        height: @vw100 * 3;
        position: relative;
        text-overflow: ellipsis;
        word-wrap: break-word;
        overflow: hidden;
      }
      .subTitle {
        opacity: 0;
        transform: translateY(@vw20);
      }
    }
  }
  .headerButton {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    transform: translateY(50%) scale(0);
    will-change: transform;
  }
}

@media screen and (max-width: 1160px) {
  .headerBlock {
    padding-bottom: calc(@vw100-1160 + @vw30-1160);
    .bigLogoWrapper {
      width: calc(@vw112-1160 * 6 + @vw16-1160 * 5);
    }
    .background {
      border-radius: @vw20-1160;
      left: @vw20-1160;
      width: calc(100% - @vw40-1160);
    }
    .contentWrapper {
      .innerContent {
        padding: calc(@vw100-1160 * 2.5) 0 calc(@vw100-1160 + @vw50-1160);
        .titleWrapper {
          margin-bottom: @vw20-1160;
          height: calc(@vw100-1160 * 3);
        }
        .subTitle {
          transform: translateY(@vw20-1160);
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .headerBlock {
    padding-bottom: calc(@vw100-580 + @vw30-580);
    .bigLogoWrapper {
      width: calc(@vw112-580 * 6 + @vw16-580 * 5);
    }
    .background {
      border-radius: @vw20-580;
      left: @vw20-580;
      width: calc(100% - @vw40-580);
      &:after {
        opacity: .9;
      }
    }
    .contentWrapper {
      .innerContent {
        padding: calc(@vw100-580 * 2.5) 0 calc(@vw100-580 + @vw50-580);
        .titleWrapper {
          margin-bottom: @vw20-580;
          height: calc(@vw100-580 * 3);
        }
        .subTitle {
          transform: translateY(@vw20-580);
        }
      }
    }
  }
}
