.homeHeaderBlock {
  position: relative;
  padding-top: 0 !important;
  margin-top: @vw100 !important;
  &.active {
    .backgroundSlides {
      .slide {
        clip-path: polygon(50% 0, 50% 0, 50% 100%, 50% 100%);
        .transitionMore(clip-path, 0s, 0.9s, ease-in-out);
        &.active { 
          clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
          z-index: 2;
          .transitionMore(clip-path, 0.9s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
          img, video {
            .transform(scale(1));
            .transitionMore(transform, 0.9s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
          }
        }
      }
    }
  }
  &.inview {
    .backgroundSlidesWrapper {
      opacity: 1;
      .transitionMore(opacity, .6s);
    }
  }
  .backgroundSlidesWrapper {
    position: relative; 
    border-radius: @vw20;
    top: 0;
    right: 0;
    min-height: calc(100vh ~"-" @vw100 ~"-" @vw30);
    height: (@vw100 * 7) + @vw30;
    margin: auto;
    width: calc(100% ~"-" @vw40);
    overflow: hidden;
    opacity: 0;
    video {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
    .backgroundSlides {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      left: 0;
      margin: auto;
      width: 100%;
      z-index: -1;
      &.active {
        width: calc(100% ~"-" @vw40);
      }
      &:after {
        content: '';
        pointer-events: none;
        background: @hardBlack;
        opacity: .4;
        width: 100%;
        top: 0;
        height: 100%;
        z-index: 3;
        position: absolute;
      }
      .slider, .sliderWrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      .slide {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        img, video {
          position: absolute;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
          .transitionMore(transform, 0.6s, .15s, cubic-bezier(0.85, 0, 0.15, 1));
          .transform(scale(1.2));
        }
      }
    }
  }
  .logoWrapper {
    background: @hardWhite;
    border-radius: @vw20;
    margin: auto;
    margin-bottom: @vw100 + @vw50;
    height: auto;
    width: (@vw112 * 2) + (@vw16 * 2);
    pointer-events: none;
    position: absolute;
    padding: @vw40;
    top: -@vw100;
    left: 0;
    right: 0;
    img {
      height: auto;
      width: 100%;
      display: block;
      margin: auto;
      pointer-events: none;
    }
  }
  .links {
    padding-bottom: @vw50;
    left: @vw104 + @vw112 + @vw16;
    bottom: 0;
    position: absolute;
    width: (@vw112 * 4) + (@vw16 * 3);
    .bigLink {
      font-family: "Local Brewery";
      font-size: @vw34;
      display: block;
      color: @hardWhite;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      padding: @vw20 0;
      padding-right: @vw50;
      cursor: pointer;
      .transition(.3s);
      &:not(.touch) {
        &:hover {
          padding-left: @vw50;
          padding-right: 0;
          &:before {
            width: 0%;
            transition-delay: 0s;
          }
          &:after {
            width: 100%;
            opacity: 1;
            transition-delay: .3s;
          }
          .arrow {
            &:first-child {
              transform: translateY(-50%) translateX(0%);
              opacity: 1;
              left: 0;
              transition-delay: .15s;
              &:before {
                width: 100%;
              }
              &:after {
              }
            }
            &:last-child {
              &:before {
                width: 0%;
                transition-delay: 0s;
              }
              &:after {
                transform: translateY(-50%) translateX(100%) rotate(45deg);
                transition-delay: 0s;
              }
            }
          }
        }
      }
      &:before, &:after {
        content: '';
        background: @hardWhite;
        position: absolute;
        height: 2px;
        bottom: 0;
        right: 0;
        opacity: 0;
        .transition(.45s);
      }
      &:before {
        opacity: .4;
        width: 100%;
        transition-delay: .15s;
      }
      &:after {
        left: 0;
        right: auto;
      }
      * {
        cursor: pointer;
      }
      .arrow {
        width: @vw50;
        height: @vw15;
        top: 50%;
        transform: translateY(-50%);
        position: absolute;
        .transition(.3s);
        &:first-child {
          width: @vw30;
          left: -3px;
          transform: translateY(-50%) translateX(-100%);
          &:before {
            width: 0%;
          }
        }
        &:last-child {
          right: 3px;
          &:before, &:after {
            transition-delay: .15s;
          }
        }
        &:before {
          content: '';
          background: @hardWhite;
          height: 2px;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          position: absolute;
          width: 100%;
          .transition(.3s);
        }
        &:after {
          content: '';
          height: @vw15;
          width: @vw15;
          border-top: 2px solid @hardWhite;
          border-right: 2px solid @hardWhite;
          right: 2px;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          position: absolute;
          .transition(.3s);
        }
      }
    }
  }
  .dots {
    // display: flex;
    display: none;
    gap: @vw16;
    margin-top: @vw50;
    .dot {
      cursor: pointer;
      display: inline-block;
      position: relative;
      width: 100%;
      height: @vw2;
      background: rgba(255,255,255,.2);
      .innerDot {
        background: @hardWhite;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 0;
      }
    }
  }
  .bottomInfo {
    display: flex;
    border-radius: @vw20 0 0 0;
    position: absolute;
    width: calc(50% ~"-" @vw8);
    right: 0;
    bottom: 0;
    background: @hardWhite;
    padding: 0 @vw30;
    height: @vw90;
    font-size: @vw16;
    flex-wrap: wrap;
    gap: @vw30;
    justify-content: left;
    align-items: center;
    .link {
      text-decoration: none;
      color: @hardBlack;
      cursor: pointer;
      .transition(.3s);
      &:hover {
        opacity: .7;
      }
      i {
        margin-right: @vw5;
      }
      span {
        text-decoration: underline;
      }
    }
  }
}
@media screen and (max-width: 1160px) {
  .homeHeaderBlock {
    margin-top: @vw100-1160 !important;
    .backgroundSlidesWrapper {
      border-radius: @vw20-1160;
      min-height: calc(100vh - @vw100-1160 - @vw30-1160);
      height: (@vw100-1160 * 7) + @vw30-1160;
      width: calc(100% - @vw40-1160);
      .backgroundSlides {
        &.active {
          width: calc(100% - @vw40-1160);
        }
      }
    }
    .logoWrapper {
      border-radius: @vw20-1160;
      margin-bottom: @vw100-1160 + @vw50-1160;
      width: (@vw112-1160 * 2) + (@vw16-1160 * 2);
      padding: @vw40-1160;
      top: -@vw100-1160;
    }
    .links {
      padding-bottom: @vw50-1160;
      bottom: @vw100-1160;
      left: @vw104-1160;
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      .bigLink {
        font-size: @vw34-1160;
        padding: @vw20-1160 0;
        padding-right: @vw50-1160;
        &:hover {
          &:not(.touch) {
            padding-left: @vw50-1160;
          }
        }
        .arrow {
          width: @vw50-1160;
          height: @vw15-1160;
          &:first-child {
            width: @vw30-1160;
          }
          &:after {
            height: @vw15-1160;
            width: @vw15-1160;
          }
        }
      }
    }
    .dots {
      gap: @vw16-1160;
      margin-top: @vw50-1160;
      .dot {
        height: @vw2-1160;
      }
    }
    .bottomInfo {
      border-radius: @vw20-1160 0 0 0;
      justify-content: space-between;
      width: 100%;
      padding: @vw30-1160;
      height: auto;
      font-size: @vw22-1160;
      bottom: -1px;
      gap: @vw30-1160;
      flex-direction: row;
      i {
        margin-right: @vw5-1160;
      }
      .link {
        display: block;
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .homeHeaderBlock {
    margin-top: @vw100-580 + @vw50-580 !important;
    .backgroundSlidesWrapper {
      border-radius: @vw20-580;
      height: calc(100vh - @vw100-580 - @vw30-580);
      min-height: (@vw100-580 * 7) + @vw30-580;
      width: calc(100% - @vw40-580);
      .backgroundSlides {
        &.active {
          width: calc(100% - @vw40-580);
        }
      }
    }
    .logoWrapper {
      border-radius: @vw20-580;
      margin-bottom: @vw100-580 + @vw50-580;
      width: (@vw112-580 * 2) + (@vw16-580 * 2);
      padding: @vw40-580;
      top: -@vw100-580;
    }
    .links {
      bottom: 0;
      padding-bottom: @vw50-580;
      left: @vw50-580;
      width: calc(100% ~"-" @vw100-580);
      .bigLink {
        font-size: @vw34-580;
        padding: @vw20-580 0;
        padding-right: @vw50-580;
        &:hover {
          &:not(.touch) {
            padding-left: @vw50-580;
          }
        }
        .arrow {
          width: @vw50-580;
          height: @vw15-580;
          &:first-child {
            width: @vw30-580;
          }
          &:after {
            height: @vw15-580;
            width: @vw15-580;
          }
        }
      }
    }
    .dots {
      gap: @vw16-580;
      margin-top: @vw50-580;
      .dot {
        height: @vw2-580;
      }
    }
    .bottomInfo {
      display: none;
    }
  }
}
