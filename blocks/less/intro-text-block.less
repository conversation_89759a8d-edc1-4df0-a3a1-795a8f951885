.introTextBlock {
  &.inview {
    .subTitle, .text {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, 1.2s, ease-in-out);
    }
  }
  .col {
    display: inline-block;
    vertical-align: top;
    &:first-child {
      width: 25%;
      padding-right: @vw48;
    }
    &:last-child {
      padding-left: @vw8;
      width: 75%;
    }
    .normalTitle {
      margin-bottom: @vw34 * 3;
    }
  }
  .subTitle, .text {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .text {
    padding: 0 @vw112 + @vw16 0 (@vw112 * 2) + (@vw16 * 2);
  }
}

@media all and (max-width: 1160px) {
  .introTextBlock {
    .col {
      &:first-child {
        padding-right: @vw48-1160;
      }
      &:last-child {
        padding-left: @vw8-1160;
      }
      .normalTitle {
        margin-bottom: @vw34-1160 * 3;
      }
    }
    .subTitle, .text {
      transform: translateY(@vw20-1160);
    }
    .text {
      padding: 0 (@vw112-1160 + @vw16-1160);
    }
  }
}

@media all and (max-width: 580px) {
  .introTextBlock {
    .col {
      &:first-child {
        width: 100%;
        margin-bottom: @vw40-580;
        padding-right: 0;
      }
      &:last-child {
        width: 100%;
        padding-left: 0;
      }
      .normalTitle {
        margin-bottom: @vw40-580;
      }
    }
    .subTitle, .text {
      transform: translateY(@vw20-580);
    }
    .text {
      padding: 0;
    }
  }
}
