.smallHeaderBlock {
  position: relative;
  margin-top: 0;
  padding-top: 0 !important;
  padding-bottom: @vw100 + @vw30;
  text-align: center;
  &.inview {
    .contentWrapper {
      .innerContent {
        .subTitle {
          opacity: 1;
          transform: translateY(0);
          .transitionMore(all, .45s, .9s, ease-in-out);
        }
      }
    }
    .headerButton {
      transform: translateY(50%) scale(1);
      .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
  }
  .contentWrapper {
    position: relative;
    .innerContent {
      padding: (@vw100 * 1.5) 0 @vw100 + @vw50;
      .titleWrapper {
        margin-bottom: @vw20;
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: flex-end;
        height: @vw100 * 3;
        position: relative;
        text-overflow: ellipsis;
        word-wrap: break-word;
        overflow: hidden;
      }
      .subTitle {
        opacity: 0;
        transform: translateY(@vw20);
      }
    }
  }
  .headerButton {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    transform: translateY(50%) scale(0);
    will-change: transform;
  }
}
@media all and (max-width: 1160px) {
  .smallHeaderBlock {
    padding-bottom: @vw100-1160 + @vw30-1160;

    &.inview {
      .contentWrapper {
        .innerContent {
          .subTitle {
            opacity: 1;
            transform: translateY(0);
            .transitionMore(all, .45s, .9s, ease-in-out);
          }
        }
      }
      .headerButton {
        transform: translateY(50%) scale(1);
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
    }

    .contentWrapper {
      .innerContent {
        padding: (@vw100-1160 * 1.5) 0 @vw100-1160 + @vw50-1160;
        .titleWrapper {
          margin-bottom: @vw20-1160;
          height: @vw100-1160 * 3;
        }
        .subTitle {
          transform: translateY(@vw20-1160);
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .smallHeaderBlock {
    padding-bottom: @vw100-580 + @vw30-580;
    &.inview {
      .contentWrapper {
        .innerContent {
          .subTitle {
            opacity: 1;
            transform: translateY(0);
            .transitionMore(all, .45s, .9s, ease-in-out);
          }
        }
      }
      .headerButton {
        transform: translateY(50%) scale(1);
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
    }

    .contentWrapper {
      .innerContent {
        padding: (@vw100-580 * 1.5) 0 @vw100-580 + @vw50-580;
        .titleWrapper {
          margin-bottom: @vw20-580;
          height: @vw100-580 * 3;
        }
        .subTitle {
          transform: translateY(@vw20-580);
        }
      }
    }
  }
}
