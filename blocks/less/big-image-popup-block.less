// out: false
.bigImagePopupBlock {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  .imageWrapper {
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: auto;
    vertical-align: top;
    width: 100%;
    .innerImage {
      padding-bottom: 48.148148148148145%;
      height: 0;
      width: 100%;
      img, video {
        position: absolute;
        object-fit: cover;
        object-position: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
    .popup {
      position: absolute;
      top: auto;
      right: @vw112 + @vw16;
      bottom: @vw112 + @vw16;
      border-radius: @vw20;
      background: @hardWhite;
      padding: @vw50;
      width: (@vw112 * 4) + (@vw16 * 3);
      .subTitle {
        margin-bottom: @vw20;
      }
      .buttonsWrapper {
        margin-top: @vw50;
        .button {
          &:not(:last-child) {
            margin-right: @vw10;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1160px) {
  .bigImagePopupBlock {
    .imageWrapper {
      .popup {
        right: @vw112-1160 + @vw16-1160;
        bottom: @vw50-1160;
        border-radius: @vw20-1160;
        padding: @vw50-1160;
        width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
        .subTitle {
          margin-bottom: @vw20-1160;
        }
        .buttonsWrapper {
          margin-top: @vw50-1160;
          .button {
            &:not(:last-child) {
              margin-right: @vw10-1160;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .bigImagePopupBlock {
    .imageWrapper {
      .innerImage {
        padding-bottom: 150%;
      }
      .popup {
        right: auto;
        left: @vw40-580;
        bottom: @vw112-580 + @vw16-580;
        border-radius: @vw20-580;
        padding: @vw50-580;
        width: calc(100% ~"-" @vw80-580);
        .subTitle {
          margin-bottom: @vw20-580;
        }
        .buttonsWrapper {
          margin-top: @vw50-580;
          .button {
            &:not(:last-child) {
              margin-right: @vw10-580;
            }
          }
        }
      }
    }
  }
}
