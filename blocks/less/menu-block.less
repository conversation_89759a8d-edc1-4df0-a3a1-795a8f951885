.menuBlock {
  .cols {
    display: flex;
    flex-wrap: wrap;
    margin-top: @vw50;
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      &:not(.image) {
        &:first-child {
          padding-right: @vw112 + @vw16;
        }
        &:last-child {
          padding-left: @vw112 + @vw16;
        }
      }
      .imageWrapper {
        cursor: zoom-in;
        display: block;
        position: relative;
        width: 100%;
        height: auto;
        * {
          cursor: zoom-in;
        }
        &:hover {
          &:before {
            opacity: .8;
          }
          &:after {
            opacity: 1;
            transform: translate(-50%,-50%) rotate(0deg);
          }
        }
        &:before {
          content: '';
          width: 100%;
          height:100%;
          position: absolute;
          top: 0;
          left: 0;
          pointer-events: none;
          background: @primaryColor;
          opacity: 0;
          z-index: 1;
          .transition(.3s);
        }
        &:after {
          content: "\e90b";
          font-family: 'icomoon';
          width: auto;
          height:auto;
          position: absolute;
          top: 50%;
          pointer-events: none;
          left: 50%;
          opacity: 0;
          transform: translate(calc(-50% ~"+" @vw50),-50%) rotate(45deg);
          color: @hardWhite;
          font-size: @vw50;
          z-index: 1;
          .transition(.3s);
        }
        img {
          display: block;
          height: auto;
          width: 100%;
          object-fit: cover;
        }
      }
      .subTitle {
        margin-bottom: @vw30;
      }
      .text {
        margin-top: @vw20;
      }
      .stickyWrapper {
        position: relative;
        height: 100%;
        .stickyElement {
          padding-top: @vw100;
          top: 0;
          position: absolute;
          width: 100%;
          height: auto;
          max-height: 100%;
        }
      }
    }
  }
}
@media all and (max-width: 1160px) {
  .menuBlock {
    .cols {
      margin-top: @vw50-1160;
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);
      .col {
        margin: 0 @vw8-1160;
        width: calc(50% ~"-" @vw16-1160);
        &:not(.image) {
          &:first-child {
            padding-right: @vw112-1160 + @vw16-1160;
          }
          &:last-child {
            padding-left: @vw112-1160 + @vw16-1160;
          }
        }
        .imageWrapper {
          &:after {
            transform: translate(calc(-50% ~"+" @vw50-1160), -50%) rotate(45deg);
            font-size: @vw50-1160;
          }
        }
        .subTitle {
          margin-bottom: @vw30-1160;
        }
        .text {
          margin-top: @vw20-1160;
        }
        .stickyWrapper {
          .stickyElement {
            padding-top: @vw100-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .menuBlock {
    .cols {
      margin-top: @vw50-580;
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &:not(:last-child) {
          margin-bottom: @vw40-580;
        }
        &:not(.image) {
          &:first-child {
            padding-right: 0;
          }
          &:last-child {
            padding-left: 0;
          }
        }
        .imageWrapper {
          &:after {
            transform: translate(calc(-50% ~"+" @vw50-580), -50%) rotate(45deg);
            font-size: @vw50-580;
          }
        }
        .subTitle {
          margin-bottom: @vw30-580;
        }
        .text {
          margin-top: @vw20-580;
        }
        .stickyWrapper {
          .stickyElement {
            position: relative;
            padding-top: 0;
          }
        }
      }
    }
  }
}
