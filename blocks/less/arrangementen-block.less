//out: false
.arrangementenBlock {
  &.inview {
    .innerContent, .items {
      opacity: 1;
      transform: translateY(0);
    }
    .items {
      transition-delay: .3s;
    }
  }
  .innerContent, .items {
    opacity: 0;
    transform: translateY(@vw20);
    .transition(.45s);
  }
  .innerContent {
    .subTitle {
      margin-bottom: @vw20;
    }
    .bigTitle {
      margin-bottom: @vw30;
    }
    .text {
      display: inline-block;
      max-width: 100%;
      width: 50%;
      padding-right: @vw8;
    }
  }
  .items {
    margin-top: @vw50;
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .item {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      .imageWrapper {
        margin-bottom: @vw30;
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: @vw20;
        height: auto;
        .innerImage {
          .padding-bottom-generator(624,416);
          height: 0;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            pointer-events: none;
          }
        }
      }
      .listItems {
        margin-top: @vw10;
        list-style: none;
        i {
          display: inline-block;
          vertical-align: top;
          color: @primaryColor;
          font-size: @vw12;
          top: @vw12;
          position: relative;
          width: @vw16;
        }
        .innerText {
          display: inline-block;
          padding-left: @vw8;
          vertical-align: top;
          width: calc(100% ~"-" @vw16);
        }
      }
    }
  }
}

@media screen and (max-width: 1160px) {
  .arrangementenBlock {
    .innerContent, .items {
      transform: translateY(@vw20-1160);
    }
    .innerContent {
      .subTitle {
        margin-bottom: @vw20-1160;
      }
      .bigTitle {
        margin-bottom: @vw30-1160;
      }
      .text {
        padding-right: @vw8-1160;
      }
    }
    .items {
      margin-top: @vw50-1160;
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);
      .item {
        margin: 0 @vw8-1160;
        width: calc(50% ~"-" @vw16-1160);
        .imageWrapper {
          margin-bottom: @vw30-1160;
          border-radius: @vw20-1160;
        }
        .listItems {
          margin-top: @vw10-1160;
          i {
            font-size: @vw12-1160;
            top: @vw12-1160;
            width: @vw16-1160;
          }
          .innerText {
            padding-left: @vw8-1160;
            width: calc(100% ~"-" @vw16-1160);
          }
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .arrangementenBlock {
    .innerContent, .items {
      transform: translateY(@vw20-580);
    }
    .innerContent {
      .subTitle {
        margin-bottom: @vw20-580;
      }
      .bigTitle {
        margin-bottom: @vw30-580;
      }
      .text {
        width: 100%;
        padding-right: 0;
      }
    }
    .items {
      margin-top: @vw50-580;
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .item {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        .imageWrapper {
          margin-bottom: @vw30-580;
          border-radius: @vw20-580;
        }
        .listItems {
          margin-top: @vw10-580;
          i {
            font-size: @vw12-580;
            top: @vw12-580;
            width: @vw16-580;
          }
          .innerText {
            padding-left: @vw8-580;
            width: calc(100% ~"-" @vw16-580);
          }
        }
      }
    }
  }
}
