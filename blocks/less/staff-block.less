// out : false
.staffBlock {
  .innerContent {
    width: (@vw112 * 5) + (@vw16 * 4);
    .text {
      margin: @vw60 0;
    }
  }
  .contentWrapper {
    position: relative;
  }
  .members {
    margin-left: -@vw25;
    margin-bottom: -@vw50;
    width: calc(100% ~"+" @vw50);
    .member {
      display: inline-block;
      margin: 0 @vw25;
      margin-bottom: @vw50;
      vertical-align: top;
      width: calc(50% ~"-" @vw50);
      &.highlighted {
        .innerCol {
          width: 100%;
          &:first-child {
            width: 100%;
          }
          .imageWrapper {
            margin-bottom: @vw22;
            .innerImage {
              .padding-bottom-generator(1,1);
            }
          }
        }
      }
      .innerCol {
        display: inline-block;
        vertical-align: middle;
        width: 65%;
        &:first-child {
          padding-right: @vw16;
          width: 35%;
        }
      }
      .subTitle {
        margin-bottom: @vw10;
      }
      .imageWrapper {
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: @vw12;
        height: auto;
        .innerImage {
          .padding-bottom-generator(3,4);
          height: 0;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            pointer-events: none;
          }
        }
      }
    }
  }
}
@media all and (max-width: 1160px) {
  .staffBlock {
    .innerContent {
      width: (@vw112-1160 * 5) + (@vw16-1160 * 4);
      .text {
        margin: @vw60-1160 0;
      }
    }
    .members {
      margin-left: -@vw25-1160;
      margin-bottom: -@vw50-1160;
      width: calc(100% ~"+" @vw50-1160);
      .member {
        margin: 0 @vw25-1160;
        margin-bottom: @vw50-1160;
        width: calc(100% ~"-" @vw50-1160);
        &.highlighted {
          margin-bottom: @vw100-1160;
          .innerCol {
            .imageWrapper {
              margin-bottom: @vw22-1160;
              .innerImage {
                .padding-bottom-generator(1,1);
              }
            }
          }
        }
        .innerCol {
          width: 65%;
          &:first-child {
            padding-right: @vw16-1160;
            width: 35%;
          }
        }
        .subTitle {
          margin-bottom: @vw10-1160;
        }
        .imageWrapper {
          border-radius: @vw12-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .staffBlock {
    .innerContent {
      width: 100%;
      .text {
        margin: @vw60-580 0;
      }
    }
    .members {
      margin-left: -@vw8-580;
      margin-bottom: -@vw80-580;
      width: calc(100% ~"+" @vw16-580);
      .member {
        margin: 0 @vw8-580;
        margin-bottom: @vw80-580;
        width: calc(100% ~"-" @vw16-580);
        &.highlighted {
          margin-bottom: @vw80-580;
          width: calc(100% ~"-" @vw16-580);
          .innerCol {
            .imageWrapper {
              margin-bottom: @vw22-580;
            }
          }
        }
        &:not(.highlighted) {
          .imageWrapper {
            .innerImage {
              .padding-bottom-generator(1, 2.2);
            }
          }
          &:nth-child(even) {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row-reverse;
            .innerCol {
              width: 55%;
              &:first-child {
                width: 45%;
                padding-right: 0;
              }
              &:last-child {
                padding-right: @vw22-580;
              }
            }
          }
        }
        .innerCol {
          width: 55%;
          &:first-child {
            padding-right: @vw22-580;
            width: 45%;
          }
        }
        .subTitle {
          margin-bottom: @vw10-580;
        }
        .imageWrapper {
          border-radius: @vw12-580;
        }
      }
    }
  }
}
