// out: false

.contactBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
  .fluentform .ff-el-group {
    margin-bottom: 0;
  }
  .contentWrapper {
    .cols {
      .col {
        vertical-align: middle;
        display: inline-block;
        width: 50%;
        &:first-child {
          padding-right: @vw112 + @vw16 + @vw8;
        }
        &:last-child {
          padding-left: @vw8;
        }
        @media (max-width: 768px) {
          width: 100%;
          margin-bottom: 20px;
        }

        .imageWrapper {
          width: 100%;
          position: relative;
          border-radius: @vw20;
          overflow: hidden;
          height: auto;
          .innerImage {
            width: 100%;
            height: 0;
            .padding-bottom-generator(500, 800);
            img, video {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              object-position: center;
            }
          }
        }

        .fluentform .ff-el-input--label.ff-el-is-required.asterisk-right label:after {
          color: @primaryColor;
        }
        

        .formWrapper {
          margin-top: @vw60;
          form {
            margin-left: -@vw8;
            width: calc(100% ~"+" @vw16);
            .field {
              margin: 0 @vw8;
              display: inline-block;
              width: calc(100% ~"-" @vw16);
              vertical-align: top;
              &.half {
                width: calc(50% ~"-" @vw16);
              }
              &.small {
                width: calc(25% ~"-" @vw16);
              }
              &.big {
                width: calc(75% ~"-" @vw16);
              }
            }

            select {
              appearance: none; /* Verbergt de standaard dropdown-pijl */
              -webkit-appearance: none;
              -moz-appearance: none;
              background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="gray"><path d="M5 7l5 5 5-5H5z"/></svg>');
              background-repeat: no-repeat;
              background-position: right 10px center;
              background-size: 16px;
              &.select2-hidden-accessible {
                width: 100%;
              }
            }

            input, select, textarea {
              width: 100%;
              padding: @vw16;
              border: none;
              border-radius: @vw20;
              font-size: @vw16;
              font-family: 'Lato', Arial, sans-serif;
              margin-bottom: @vw20;
              box-sizing: border-box;
              background: @lightGrey;
              &:focus {
                border-color: none;
                outline: none;
              }

              &::-webkit-input-placeholder {
                color: #999;
              }

              &:-moz-placeholder {
                color: #999;
              }

              &::-moz-placeholder {
                color: #999;
              }

              &:-ms-input-placeholder {
                color: #999;
              }

              &::placeholder {
                color: #999;
              }

            }

            textarea {
              resize: none;
            }

            button[type="submit"] {
              background-color: @primaryColor;
              color: @hardWhite;
              padding: @vw16;
              line-height: 1;
              display: block;
              width: 100%;
              font-size: @vw20;
              border: 1px solid @primaryColor;
              border-radius: @vw100;
              cursor: pointer;
              .transition(.3s);
              &:hover {
                background-color: @hardWhite;
                color: @primaryColor;
              }
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
            }
            .select2-container--default .select2-selection--single {
              height: @vw40;
              border: 1px solid #ddd;
              border-radius: @vw20;
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
              padding-left: @vw10;
              line-height: 1;
            }

            .select2-container .select2-selection--single .select2-selection__arrow {
              height: @vw40;
              right: @vw10;
            }
            .error {
              color: #ff0000;
              background-color: #ffe6e6;
              border: 1px solid #ff0000;
              padding: 10px;
              border-radius: 4px;
              margin-top: 5px;
              font-family: 'Lato', Arial, sans-serif;
              font-size: @vw12;
              display: block;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .contactBlock {
    &.inview {
      .subTitle, .text, .imageWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .subTitle, .text, .imageWrapper {
      opacity: 0;
      transform: translateY(@vw20-1160);
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .text {
      margin-top: @vw30-1160;
    }
    .contentWrapper {
      .cols {
        .col {
          vertical-align: middle;
          display: inline-block;
          width: 50%;
          &:first-child {
            padding-right: @vw40-1160;
          }
          &:last-child {
            padding-left: @vw8-1160;
          }
          .imageWrapper {
            width: 100%;
            position: relative;
            border-radius: @vw20-1160;
            overflow: hidden;
            height: auto;
            .innerImage {
              width: 100%;
              height: 0;
              .padding-bottom-generator(500, 800);
              img, video {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
              }
            }
          }

          .formWrapper {
            margin-top: @vw60-1160;
            form {
              margin-left: -@vw8-1160;
              width: calc(100% ~"+" @vw16-1160);
              .field {
                white-space: nowrap;
                .subtitle {
                  white-space: normal;
                }
                margin: 0 @vw8-1160;
                display: inline-block;
                width: calc(100% ~"-" @vw16-1160);
                vertical-align: top;
                &.half {
                  width: calc(50% ~"-" @vw16-1160);
                }
                &.small {
                  width: calc(25% ~"-" @vw16-1160);
                }
              }

              select {
                &.select2-hidden-accessible {
                  width: 100%;
                }
              }

              input, select, textarea {
                width: 100%;
                padding: @vw16-1160;
                border: none;
                border-radius: @vw20-1160;
                font-size: @vw16-1160;
                font-family: 'Lato', Arial, sans-serif;
                margin-bottom: @vw20-1160;
                box-sizing: border-box;
                background: @lightGrey;
                &:focus {
                  border-color: none;
                  outline: none;
                }

                &::-webkit-input-placeholder {
                  color: #999;
                }

                &:-moz-placeholder {
                  color: #999;
                }

                &::-moz-placeholder {
                  color: #999;
                }

                &:-ms-input-placeholder {
                  color: #999;
                }

                &::placeholder {
                  color: #999;
                }
              }

              textarea {
                resize: none;
              }

              button[type="submit"] {
                background-color: @primaryColor;
                color: @hardWhite;
                padding: @vw16-1160;
                font-size: @vw20-1160;
                line-height: 1;
                border: 1px solid @primaryColor;
                border-radius: @vw20-1160;
                cursor: pointer;
                .transition(.3s);
                &:hover {
                  background-color: @hardWhite;
                  color: @primaryColor;
                }
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
              }

              .select2-container--default .select2-selection--single {
                height: @vw40-1160;
                border: 1px solid #ddd;
                border-radius: @vw20-1160;
              }

              .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: @vw10-1160;
                line-height: 1;
              }

              .select2-container .select2-selection--single .select2-selection__arrow {
                height: @vw40-1160;
                right: @vw10-1160;
              }

              .error {
                color: #ff0000;
                background-color: #ffe6e6;
                border: 1px solid #ff0000;
                padding: 10px;
                border-radius: 4px;
                margin-top: 5px;
                font-family: 'Lato', Arial, sans-serif;
                font-size: @vw12-1160;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    &.inview {
      .subTitle, .text, .imageWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .subTitle, .text, .imageWrapper {
      opacity: 0;
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
    .contentWrapper {
      .cols {
        .col {
          vertical-align: middle;
          display: inline-block;
          width: 100%;
          &:first-child {
            margin-bottom: @vw40-580;
            padding-right:0;
          }
          &:last-child {
            padding-left: 0;
          }
          .imageWrapper {
            width: 100%;
            position: relative;
            border-radius: @vw20-580;
            overflow: hidden;
            height: auto;
            .innerImage {
              width: 100%;
              height: 0;
              .padding-bottom-generator(1, 1);
              img, video {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
              }
            }
          }

          .formWrapper {
            margin-top: @vw60-580;
            form {
              margin-left: -@vw8-580;
              width: calc(100% ~"+" @vw16-580);
              .field {
                margin: 0 @vw8-580;
                display: inline-block;
                width: calc(100% ~"-" @vw16-580);
                vertical-align: top;
                &.big {
                  width: calc(66.6666% ~"-" @vw16-580);
                }
                &.half {
                  width: calc(100% ~"-" @vw16-580);
                }
                &.small {
                  width: calc(33.3333% ~"-" @vw16-580);
                }
              }

              select {
                &.select2-hidden-accessible {
                  width: 100%;
                }
              }

              input, select, textarea {
                width: 100%;
                padding: @vw22-580;
                border: none;
                border-radius: @vw20-580;
                font-size: @vw22-580;
                font-family: 'Lato', Arial, sans-serif;
                margin-bottom: @vw20-580;
                box-sizing: border-box;
                background: @lightGrey;
                &:focus {
                  border-color: none;
                  outline: none;
                }

                &::-webkit-input-placeholder {
                  color: #999;
                }

                &:-moz-placeholder {
                  color: #999;
                }

                &::-moz-placeholder {
                  color: #999;
                }

                &:-ms-input-placeholder {
                  color: #999;
                }

                &::placeholder {
                  color: #999;
                }
              }

              input {
                height: @vw60-580;
                line-height: @vw60-580;
                display: block;
              }

              textarea {
                resize: none;
              }

              button[type="submit"] {
                background-color: @primaryColor;
                color: @hardWhite;
                padding: @vw22-580;
                font-size: @vw20-580;
                line-height: 1;
                border: 1px solid @primaryColor;
                border-radius: @vw100-580;
                cursor: pointer;
                .transition(.3s);
                &:hover {
                  background-color: @hardWhite;
                  color: @primaryColor;
                }
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
              }

              .select2-container--default .select2-selection--single {
                height: @vw40-580;
                border: 1px solid #ddd;
                border-radius: @vw20-580;
              }

              .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: @vw10-580;
                line-height: 1;
              }

              .select2-container .select2-selection--single .select2-selection__arrow {
                height: @vw40-580;
                right: @vw10-580;
              }

              .error {
                color: #ff0000;
                background-color: #ffe6e6;
                border: 1px solid #ff0000;
                padding: 10px;
                border-radius: 4px;
                margin-top: 5px;
                font-family: 'Lato', Arial, sans-serif;
                font-size: @vw16-580;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}
