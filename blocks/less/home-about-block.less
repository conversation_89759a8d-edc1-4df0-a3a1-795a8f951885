//out: false

.homeAboutBlock {
  padding: @vw100 + @vw50 0;
  &:before {
    content: '';
    background: @hardBlack;
    border-radius: @vw40;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }  
  .cols {
    .col {
      &.top {
        .imageWrapper {
          width: (@vw112 * 7) + (@vw16 * 6);
        }
        .textWrapper {
          width: calc(100% - ((@vw112 * 7) + (@vw16 * 6)));
        }
      }
      &.bottom {
        display: flex;
        flex-wrap: wrap;
        .imageWrapper {
          margin-top: @vw100 + (@vw65 * 2);
          width: (@vw112 * 8) + (@vw16 * 7);
          .innerImage {
            padding-bottom: 72.22222222222223%;
          }
        }
        .textWrapper {
          position: relative;
          height: auto;
          width: calc(100% - ((@vw112 * 8) + (@vw16 * 7)));
          padding-right: 0;
        }
      }
    }
  }
  .textWrapper {
    padding: 0 @vw40 0 @vw112 + @vw16;
    display: inline-block;
    max-width: 100%;
    vertical-align: top;
    top: 0;
    .subTitle {
      position: relative;
      margin-bottom: @vw20;
    }
    .text {
      padding-top: @vw65;
      position: absolute;
    }
    .buttonWrapper {
      display: block;
      margin-top: @vw60;
    }
  }
  .imageWrapper {
    border-radius: @vw40;
    display: inline-block;
    position: relative;
    overflow: hidden;
    height: auto;
    vertical-align: top;
    max-width: 100%;
    .innerImage {
      padding-bottom: 41.47727272727273%;
      height: 0;
      width: 100%;
      img, video {
        position: absolute;
        object-fit: cover;
        object-position: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
}

@media screen and (max-width: 1160px) {
  .homeAboutBlock {
    padding: @vw100-1160 + @vw50-1160 0;
    &:before {
      border-radius: @vw40-1160;
    }
    .cols {
      .col {
        &.top {
          .imageWrapper {
            width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
          }
          .textWrapper {
            padding: 0;
            padding-right: @vw40-1160;
            width: calc(100% ~"-" ((@vw112-1160 * 5) + (@vw16-1160 * 4)));
          }
        }
        &.bottom {
          .imageWrapper {
            margin-top: @vw100-1160 + (@vw65-1160 * 2);
            width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
          }
          .textWrapper {
            width: calc(100% ~"-" ((@vw112-1160 * 5) + (@vw16-1160 * 4)));
          }
        }
      }
    }
    .textWrapper {
      padding: 0 @vw40-1160 0 @vw112-1160 + @vw16-1160;
      .subTitle {
        margin-bottom: @vw20-1160;
      }
      .text {
        padding-top: @vw65-1160;
      }
      .buttonWrapper {
        margin-top: @vw60-1160;
      }
    }
    .imageWrapper {
      border-radius: @vw40-1160;
    }
  }
}

@media screen and (max-width: 580px) {
  .homeAboutBlock {
    padding: @vw100-580 + @vw50-580 0;
    &:before {
      border-radius: @vw40-580;
    }
    .cols {
      .col {
        &.top {
          .imageWrapper {
            display: none;
          }
          .textWrapper {
            width: 100%;
          }
        }
        &.bottom {
          display: block;
          .imageWrapper {
            margin-top: @vw40-580;
            width: 100%;
          }
          .textWrapper {
            width: 100%;
          }
        }
      }
    }
    .textWrapper {
      padding: 0;
      .subTitle {
        margin-bottom: @vw20-580;
      }
      .text {
        position: relative;
        padding-top: @vw65-580;
      }
      .buttonWrapper {
        margin-top: @vw60-580;
      }
    }
    .imageWrapper {
      border-radius: @vw40-580;
    }
  }
}
