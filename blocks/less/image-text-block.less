.imageTextBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      &.text {
          padding: 0 @vw112 + @vw16;
      }
      &:not(.text) {
        &:first-child {
          padding-left: @vw112 + @vw16;
          .imageWrapper {
            .innerImage {
              .padding-bottom-generator(624, 528);
            }
          }
        }
      }
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    position: relative;
    border-radius: @vw20;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .padding-bottom-generator(752, 528);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1160px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);
      .col {
        margin: 0 @vw8-1160;
        width: calc(50% ~"-" @vw16-1160);
        &.text {
          padding: 0 @vw40-1160;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1160;
            .imageWrapper {
              .innerImage {
                .padding-bottom-generator(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1160);
    }
    .imageWrapper {
      border-radius: @vw20-1160;
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .text {
      margin-top: @vw30-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
            .imageWrapper {
              .innerImage {
                .padding-bottom-generator(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .imageWrapper {
      border-radius: @vw20-580;
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}
