<?php
$size = 'full'; // (thumbnail, medium, large, full or custom size)
$logo = get_field("logo");
$video = get_field("video");
?>
<section class="homeHeaderBlock dark<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="backgroundSlidesWrapper">
    <div class="backgroundSlides">
      <div class="sliderWrapper">
        <?php if(!get_field("video")): ?>
        <div class="slider">
          <?php if( have_rows('slide') ): ?>
            <?php while( have_rows('slide') ): the_row(); ?>
            <?php $i = 0; $active_class = ($i === 0) ? ' active' : ''; ?>
              <?php $video = get_sub_field('video'); ?>
              <?php $image = get_sub_field('image'); ?>
              <?php if ($video) { ?>
                <div class="slide<?php echo $active_class; ?>">
                  <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                    <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                  </video>
                </div>
              <?php } else { if (get_sub_field('image')){ ?>
                <?php $image = get_sub_field('image'); ?>
                <div class="slide">
                  <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                </div>
              <?php } } ?>
              <?php $i++; ?>
            <?php endwhile; ?>
          <?php endif; ?>
        </div>
      <?php else: ?>
      <video class="video" muted playsinline loop autoplay>
        <source src="<?php echo esc_url($video); ?>" type="video/mp4">
      </video>
    <?php endif; ?>
      </div>
    </div>
  </div>
  <div class="logoWrapper">
    <img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" />
  </div>
  <div class="links">
    <?php
      render_button('link_1');
      render_button('link_2');
      render_button('link_3');
    ?>
    <?php if(!get_field("video")): ?>
    <div class="dots">
      <?php if( have_rows('slide') ): ?>
        <?php while( have_rows('slide') ): the_row(); ?>
          <?php $image = get_sub_field('image'); ?>
          <?php if ($image) { ?>
            <span class="dot" data-slider-dot><span class="innerDot"></span></span>
          <?php } ?>
        <?php endwhile; ?>
      <?php endif; ?>
    </div>
    <?php endif; ?>
  </div>
  <div class="bottomInfo">
    <a class="link" title="<?php echo get_theme_mod('customTheme-main-callout-address') ?>" href="<?php echo get_theme_mod('customTheme-main-callout-maps-link') ?>" target="_blank"><i class="icon-location"></i><span><?php echo get_theme_mod('customTheme-main-callout-address') ?></span></a>
    <a class="link" title="<?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="tel:<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank"><i class="icon-phone"></i><span><?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?></span></a>
    <a class="link" title="Whatsapp: <?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="https://wa.me/<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank">
        <i class="icon-whatsapp"></i><span><?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?></span>
    </a>
  </div>
</section>
