<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)

  $menu = get_field('menu');

  $title = get_field('title');
  $image_right = get_field('image_right');
  $image = get_field('menu_image', $menu->ID);
  $pdf_link = get_field('menu_pdf', $menu->ID);
  $uid = uniqid('', true);
?>
<section class="menuBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="cols">
      <?php if (!$image_right): ?>
      <div class="col image">
        <?php if ($image): ?>
            <div class="imageWrapper" data-gallery data-gallery-id="menus123" data-image="<?php echo esc_url($image['url']); ?>">
                <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            </div>
        <?php endif; ?>
      </div>
      <?php endif; ?>
      <div class="col">
        <div class="stickyWrapper">
          <div class="stickyElement">
            <?php if (get_field("subtitle")) {?><div class="subTitle primary"><?php the_field("subtitle") ?></div><?php } ?>
            <h2 class="bigTitle splitThis" data-init data-split><?php echo esc_html(get_the_title($menu->ID)); ?></h2>
            <?php if (get_field("text")) {?>
              <div class="text">
                <?php the_field("text") ?>
              </div>
            <?php } ?>
            <div class="buttons">
              <?php if ($pdf_link): ?>
                <a href="download:<?php echo esc_url($pdf_link); ?>" target="_blank" class="button">
                    download
                </a>
              <?php endif; ?>
            </div>
          </div>
        </div>
      </div>
      <?php if ($image_right): ?>
      <div class="col image">
        <?php if ($image): ?>
          <div class="imageWrapper" data-gallery data-gallery-id="menus123" data-image="<?php echo esc_url($image['url']); ?>">
              <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
          </div>
        <?php endif; ?>
      </div>
      <?php endif; ?>
    </div>
  </div>
</section>
<?php if (get_field('overlay')): ?>
<div class="overlay" style="display: none;" data-gallery-id="menus123">
  <span class="background"></span>
  <span class="close">&times;</span>
  <div class="overlayContent">
    <div class="overlayImage">
        <img src="" alt="" id="overlayImg">
    </div>
  </div>
  <div class="navButton prev"><i class="icon-long-arrow-left"></i></div>
  <div class="navButton next"><i class="icon-long-arrow-right"></i></div>
</div>
<?php endif; ?>