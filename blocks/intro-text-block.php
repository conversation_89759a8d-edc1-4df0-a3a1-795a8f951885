<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("intro"));
?>

<section class="introTextBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <div class="subTitle primary"><?php the_field("subtitle") ?></div>
      </div>
      <div class="col">
        <h2 class="normalTitle splitThis" data-init data-split><?php echo $title; ?></h2>
        <div class="text">
          <?php the_field("text") ?>
        </div>
      </div>
    </div>
  </div>
</section>
