<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));

// Voer regex vervanging uit op de title (indien nodig)
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="imageTextBlock <?php echo esc_attr(get_field("background")); ?><?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <?php if (!get_field("image_right") && $image) { ?>
        <div class="col image">
          <div class="imageWrapper">
            <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
            </div>
          </div>
        </div>
      <?php } ?>
      <div class="col text">
        <div class="subTitle primary">
          <?php echo esc_html(get_field("subtitle")); ?>
        </div>
        <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
        <div class="text">
          <?php echo get_field("text"); ?>
        </div>
      </div>
      <?php if (get_field("image_right") && $image) { ?>
        <div class="col image">
          <div class="imageWrapper">
            <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
            </div>
          </div>
        </div>
      <?php } ?>
    </div>
  </div>
</section>
