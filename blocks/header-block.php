<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$bigLogo = get_field("show_big_logo");
?>

<section class="headerBlock dark header-block<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <?php if ($bigLogo) { ?>
  <div class="bigLogoWrapper">
    <div class="svgWrapper">
      <?php include("parts/big_logo.php");  ?>
    </div>
  </div>
<?php } ?>
  <div class="background">
    <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
  </div>
  <div class="contentWrapper small">
    <div class="innerContent">
      <div class="titleWrapper">
        <h1 class="hugeTitle white splitThis" data-init data-split><?php the_field("title") ?></h1>
      </div>
      <h2 class="subTitle primary"><?php the_field("subtitle") ?></h2>
    </div>
  </div>
  <?php include("parts/header_button.php");  ?>
</section>
