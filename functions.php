<?php

// Include custom post types and ACF fields
require_once get_template_directory() . '/includes/custom-post-types.php';
require_once get_template_directory() . '/includes/acf-fields.php';
require_once get_template_directory() . '/includes/whatsapp-settings.php';
require_once get_template_directory() . '/includes/add-gallery-field.php';

function jn_enqueue_assets() {
  $scripts = array(
    'Swup' => '/libs/swup.js',
    'Swup_head' => '/libs/swup_head.js',
    'Swup_Gtag' => '/libs/swup_gtag.js',
    'Swup GA' => '/libs/SwupGaPlugin.min.js',
    'Jquery' => '/libs/jquery.min.js',
    'Lenis' => '/libs/lenis.min.js',
    'Select2' => '/libs/select2.min.js',
    'Gsap' => '/libs/gsap.min.js',
    'Custom_Ease' => '/libs/CustomEase.min.js',
    'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
    'SplitText' => '/libs/SplitText.min.js',
    'Flickity_js' => '/libs/flickity.min.js',
    'Hammer_js' => '/libs/hammer.min.js',
    'main_js' => '/assets/js/main.js',
    'Header' => '/assets/js/header.js',
    'Footer' => '/assets/js/footer.js',
    'Gallery' => '/assets/js/parts/gallery.js',
    'Slider' => '/assets/js/parts/slider.js',
    'parallax' => '/assets/js/parts/parallax.js',
    'speed' => '/assets/js/parts/form.js',
    'Popup' => '/assets/js/parts/popup.js',
    'StickyWhatsApp' => '/assets/js/parts/sticky-whatsapp.js',
    'Header_Block_JS' => '/blocks/js/header-block.js',
    'Home_About_Block_JS' => '/blocks/js/home-about-block.js',
    'Home_Header_Block_JS' => '/blocks/js/home-header-block.js',
    'Home_Intro_Block_JS' => '/blocks/js/home-intro-block.js',
    'Menu_Block_JS' => '/blocks/js/menu-block.js',
    'Menu_highlight_Block_JS' => '/blocks/js/menu-highlight-block.js',
    'Small_Header_Block_JS' => '/blocks/js/small-header-block.js',
    'Sticky_Date_Text_Block_JS' => '/blocks/js/sticky-date-text-block.js',
    'CONTACT_BLOCK_JS' => '/blocks/js/contact-block.js',
  );

  foreach ($scripts as $handle => $path) {
    wp_enqueue_script($handle, get_theme_file_uri($path), array(), false, true);
  }

  wp_enqueue_style('main', get_stylesheet_uri());
  // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
  echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
  $sections = array(
    'customTheme-main-callout-title' => 'Title',
    'customTheme-main-callout-description' => 'Description',
    'customTheme-main-callout-featured-image' => 'Image',
    'customTheme-main-callout-logo' => 'Logo',
    'customTheme-main-callout-logo-white' => 'Logo (White)',
    'customTheme-main-callout-telephone' => 'Telephone',
    'customTheme-main-callout-telephone-label' => 'Telephone label',
    'customTheme-main-callout-mail' => 'Mail',
    'customTheme-main-callout-address' => 'Address',
    'customTheme-main-callout-maps-link' => 'Google maps link',
    'customTheme-main-callout-facebook' => 'Facebook URL',
    'customTheme-main-callout-tiktok' => 'Tiktok URL',
    'customTheme-main-callout-instagram' => 'Instagram URL',
    'customTheme-main-callout-analytics' => 'Analytics ID',
    'customTheme-main-callout-contact-title' => 'Contact title',
    'customTheme-main-callout-contact-text' => 'Contact text',
    'customTheme-main-callout-reserve-link' => 'Reserve link (url)',
    'customTheme-main-callout-reserve-label' => 'Reserve label',
    'customTheme-main-callout-menu-image' => 'Menu image',
  );

  $wp_customize->add_section('customTheme-main-callout-section', array(
    'title' => 'Main Information'
  ));

  foreach ($sections as $setting_id => $label) {
    $wp_customize->add_setting($setting_id);
    $control_args = array(
      'label' => $label,
      'section' => 'customTheme-main-callout-section',
      'settings' => $setting_id
    );

    if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || strpos($setting_id, 'menu-image') !== false) {
      $control_args['width'] = 750;
      $control_args['height'] = 500;
      $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
    } elseif ($label === 'Description' || $label === 'Contact text') {
      $control_args['type'] = 'textarea';
      $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
    } else {
      $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
    }
  }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
  register_nav_menus(array(
    'primary-menu' => 'Primary Menu',
    'secondary-menu' => 'Secondary Menu',
    'footer-menu-1' => 'Footer menu 1',
    'footer-menu-2' => 'Footer menu 2',
    'footer-menu-3' => 'Footer menu 3',
    'footer-bottom-menu' => 'Footer bottom menu'
  ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
  unset($robots['max-image-preview']);
  return $robots;
}

// blocks

function get_default_block_icon() {
  return '<svg id="Group_346" data-name="Group 346" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="24.578" viewBox="0 0 25 24.578">
  <defs>
  <clipPath id="clip-path">
  <rect id="Rectangle_9" data-name="Rectangle 9" width="25" height="24.578" fill="#232020"/>
  </clipPath>
  </defs>
  <g id="Group_146" data-name="Group 146" clip-path="url(#clip-path)">
  <path id="Path_1986" data-name="Path 1986" d="M24.535,13.879a10.419,10.419,0,0,1-1.974,5.546A11.971,11.971,0,0,1,17.8,23.431a11.711,11.711,0,0,1-4.466,1.122,7.091,7.091,0,0,1-3.362-.426,19.943,19.943,0,0,0-2.742-.741,3.554,3.554,0,0,1-1.208-.595,19.886,19.886,0,0,1-2.953-2.6C1.55,18.335.044,16.409.138,13.794c.007-.2.183-.383.214-.584a1.487,1.487,0,0,0,.009-.82A4.009,4.009,0,0,1,.38,9.047c.316-.9.707-1.783.957-2.7a5.523,5.523,0,0,1,1.4-2.122A9.537,9.537,0,0,1,4.316,2.662a10.677,10.677,0,0,1,1.895-.925A19.519,19.519,0,0,1,8.954.574,11.563,11.563,0,0,1,12.463.006a14.174,14.174,0,0,1,8.149,2.849,10.452,10.452,0,0,1,3.958,6.829,2.175,2.175,0,0,1,.035.873L18.437,4.391A6.2,6.2,0,0,0,19.707,7.7a9.594,9.594,0,0,0,.775.915c.393.452.771.878,1.492.592a.84.84,0,0,1,.715.238c.751.814,1.457,1.67,2.312,2.665-.966,0-1.737.026-2.5-.018-.194-.011-.368-.257-.565-.371A2.419,2.419,0,0,0,21,11.25a20,20,0,0,0-2.275.113c-.086.006-.166.092-.3.171,1.077.267,2.177-.213,3.153.291l-.005.254H18.452l0,.337h3.162q.005.175.01.351c-1.027.3-2.121-.006-3.187.3.445.069.887.161,1.334.2a8.8,8.8,0,0,0,1.458.037,1,1,0,0,0,.622-.338,1.281,1.281,0,0,1,1.17-.469c.568.034,1.139.008,1.844.008-.341.449-.588.83-.89,1.16-.772.845-1.545,1.694-2.373,2.483a1.385,1.385,0,0,1-.972.3,2.3,2.3,0,0,0-2.318,2.184.942.942,0,0,0,.95,1.034,2.521,2.521,0,0,0,2.348-1.752.21.21,0,0,0,.013-.125c-.47-1.2.508-1.665,1.165-2.288.578-.548,1.167-1.085,1.751-1.626M9.039,2.357a.915.915,0,0,1-.249.033c-1.817-.27-3.5-.171-4.54,1.629.225.893.988,1.008,1.67,1.012.266,0,.519-.565.807-.842.7-.669,1.079-1.766,2.306-1.728,0-.039-.005-.077-.005-.114.874-.069,1.748-.154,2.624-.2.584-.031.683-.4.744-.867-.113-.016-.175-.031-.238-.033a4.859,4.859,0,0,0-3.119,1.11m-.23,8.459c1.073-.681,2.075-1.321,3.081-1.953.5-.315.644-.235.647.375.007,1.322,0,2.644,0,3.966v1.7l-.764.067v4.245c-.425.182-.972-.178-1.064.494a.492.492,0,0,1-.389.058c-.578-.383-.99-.017-1.457.244,2.616.92,5.283.681,8.089.667a5.56,5.56,0,0,0-3.775-1.443c0-1.171-.049-2.3.015-3.426a2.477,2.477,0,0,1,1.34-2.387,3.479,3.479,0,0,0,1.837-2.1,8.584,8.584,0,0,0,.372-4.65c-.168-.887-.47-1.749-.7-2.572H12.534V6.518a4.333,4.333,0,0,0-.545-.144c-.189-.023-.39.02-.573-.021a.641.641,0,0,1-.392-.234c-.462-.805-.557-.883-1.4-.519-.562.243-1.292.393-1.423,1.14a9.224,9.224,0,0,0,.133,3.817,12.4,12.4,0,0,0,.589,1.532,1.165,1.165,0,0,0,.019-.661c-.043-.218-.095-.434-.134-.612m-7.38,4.626a2.817,2.817,0,0,0,.481,0,1.326,1.326,0,0,1,1.684,1.286c.062.6.274,1.175.35,1.77a.649.649,0,0,0,.7.635,1.268,1.268,0,0,0,.822-.435,1.553,1.553,0,0,0,.169-1.545A11.164,11.164,0,0,1,5.7,11.642l-1-.081a.876.876,0,0,0-.089.191c-.133.8-.238,1.608-.411,2.4a.64.64,0,0,1-.492.368A5.688,5.688,0,0,1,2.617,14.3c-.9-.206-1.207.064-1.188,1.144m9.909,1.8c-.212,0-.4-.008-.593,0a1,1,0,0,1-1.12-.677,5.009,5.009,0,0,0-.547-.982c-.108-.159-.313-.382-.452-.367a4.4,4.4,0,0,0-2.538.638L6.8,17.227l.165-.03a3.706,3.706,0,0,0,.068-.706.648.648,0,0,1,.474-.776c.426-.138.84-.446,1.3-.089a1.365,1.365,0,0,1-.216,2.31,2.9,2.9,0,0,0-.5.259c-.141.116-.341.307-.32.434a.634.634,0,0,0,.4.393,7.323,7.323,0,0,0,1.7.362c.459.017.994-.04,1.091-.729a11.146,11.146,0,0,1,.385-1.414M8.511,14.406c-.046-.172-.077-.337-.133-.493A15.713,15.713,0,0,1,7.017,8.623c0-.106-.334-.334-.4-.3-.495.274-.961.6-1.47.931.546.454.959.842,1.416,1.167.664.474.633.994.317,1.678a22.509,22.509,0,0,0-.788,2.305Zm-2,6.151a19.667,19.667,0,0,1,2.116,1.076,18.3,18.3,0,0,1,1.718,1.427c.143-.16.372-.619.726-.759a3.445,3.445,0,0,1,1.319.031A1.572,1.572,0,0,0,10.8,20.825c-.946-.009-1.892,0-2.979,0l.445-.742c-.624-.059-1.252-.191-1.758.476m-.5-14.2a2.85,2.85,0,0,1-2.752-1.8L2.464,6.082c1.093,1.139,2.152,1.9,3.414.476.027-.031.045-.07.132-.2m6.382-3.016c-3.373-.356-4.476.46-5.343,2.1a.874.874,0,0,1,1,.818,17.535,17.535,0,0,0,.642-1.892c.1-.356.283-.447.631-.44.958.02,1.916.005,2.874,0a.745.745,0,0,0,.192-.06ZM3.231,13.489c.162-.717.285-1.308.43-1.894.226-.91.1-1.377-.513-1.715-.164.839-.2,1.762-1.233,2.038-.04.962.805,1.053,1.317,1.571M.744,11.348c.258-.241.54-.394.648-.628.287-.625.5-1.285.739-1.932l.136.051L1.86,8.075c-.568,1-1.621,1.786-1.115,3.273m1.042,6.19,1.758,2.543.171-.108L2.433,17.116l-.647.422m6.791,5.654.122-.251L5.847,21.423l-.133.248,2.862,1.522m2.094-8.376.866-.05-1.011-1.173.146,1.224" transform="translate(0 0)" fill="#232020"/>
  <path id="Path_1987" data-name="Path 1987" d="M67.242,54.4c-.224.361-.006.722.345,1.037.28.251.322.427-.049.662-.49.312-.925.707-1.4,1.043a.686.686,0,0,1-1.121-.537,13.636,13.636,0,0,1,.142-2c.018-.133.309-.323.474-.323.526,0,1.052.073,1.608.122" transform="translate(-56.687 -47.323)" fill="#232020"/>
  </g>
  </svg>
  ';
}

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

  // Check function exists.
  if( function_exists('acf_register_block_type') ) {

    // Register a testimonial block.
    acf_register_block_type(array(
        'name'              => 'home_header_block',
        'title'             => __('Home header Block'),
        'render_template'   => 'blocks/home-header-block.php',
        'category'          => 'headers',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/home-header-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'home_intro_block',
        'title'             => __('Home intro Block'),
        'render_template'   => 'blocks/home-intro-block.php',
        'category'          => 'home',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/home-intro-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'divider_block',
        'title'             => __('Divider Block'),
        'render_template'   => 'blocks/divider-block.php',
        'category'          => 'divider',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/divider-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'menus_block',
        'title'             => __('Menus Block'),
        'render_template'   => 'blocks/menus-block.php',
        'category'          => 'menus',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/menus-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'home_about_block',
        'title'             => __('Home about Block'),
        'render_template'   => 'blocks/home-about-block.php',
        'category'          => 'about',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/home-about-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'images_grid_text_block',
        'title'             => __('Images grid text Block'),
        'render_template'   => 'blocks/images-grid-text-block.php',
        'category'          => 'image',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/images-grid-text-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'header_block',
        'title'             => __('Header Block'),
        'render_template'   => 'blocks/header-block.php',
        'category'          => 'header',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/header-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'small_header_block',
        'title'             => __('Small Header Block'),
        'render_template'   => 'blocks/small-header-block.php',
        'category'          => 'header, small',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/small-header-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'quote_block',
        'title'             => __('Quote Block'),
        'render_template'   => 'blocks/quote-block.php',
        'category'          => 'quote',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/quote-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'two_col_text_block',
        'title'             => __('Two column text Block'),
        'render_template'   => 'blocks/two-col-text-block.php',
        'category'          => 'text',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/two-col-text-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'gallery_block',
        'title'             => __('Gallery Block'),
        'render_template'   => 'blocks/gallery-block.php',
        'category'          => 'gallery',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/gallery-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'intro_text_block',
        'title'             => __('Intro text Block'),
        'render_template'   => 'blocks/intro-text-block.php',
        'category'          => 'text',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/intro-text-block.css' : '',
    ));

    acf_register_block_type(array(
        'name'              => 'quote_image_block',
        'title'             => __('Quote image Block'),
        'render_template'   => 'blocks/quote-image-block.php',
        'category'          => 'quote,image',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/quote-image-block.css' : '',
    ));
    acf_register_block_type(array(
        'name'              => 'staff_slider_block',
        'title'             => __('Staff Slider Block'),
        'render_template'   => 'blocks/staff-slider-block.php',
        'category'          => 'slider',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/staff-slider-block.css' : '',
    ));
    acf_register_block_type(array(
      'name'              => 'staff_block',
      'title'             => __('Staff Block'),
      'render_template'   => 'blocks/staff-block.php',
      'category'          => 'staff',
      'icon'              => get_default_block_icon(),
  ));
    acf_register_block_type(array(
        'name'              => 'image_text_block',
        'title'             => __('Image Text Block'),
        'render_template'   => 'blocks/image-text-block.php',
        'category'          => 'Image',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/image-text-block.css' : '',
    ));
    acf_register_block_type(array(
        'name'              => 'arrangementen_block',
        'title'             => __('Arrangementen Block'),
        'render_template'   => 'blocks/arrangementen-block.php',
        'category'          => 'Arrangementen',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/arrangementen-block.css' : '',
    ));
    acf_register_block_type(array(
        'name'              => 'menu_block',
        'title'             => __('Menu Block'),
        'render_template'   => 'blocks/menu-block.php',
        'category'          => 'formatting',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/menu-block.css' : '',
        'keywords'          => array('menu', 'food'),
    ));
    acf_register_block_type(array(
        'name'              => 'contact_details_block',
        'title'             => __('Contact Details Block'),
        'render_template'   => 'blocks/contact-details-block.php',
        'category'          => 'formatting',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/contact-details-block.css' : '',
        'keywords'          => array('contact', 'details', 'address'),
    ));
    acf_register_block_type(array(
        'name'              => 'contact_block',
        'title'             => __('Contact Block'),
        'render_template'   => 'blocks/contact-block.php',
        'category'          => 'formatting',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/contact-block.css' : '',
        'keywords'          => array('contact', 'details', 'form', 'phone'),
    ));
    acf_register_block_type(array(
        'name'              => 'opening_times_block',
        'title'             => __('Opening Times Block'),
        'render_template'   => 'blocks/openingtimes-block.php', // Pad naar je template bestand
        'category'          => 'formatting',
        'icon'              => 'clock', // Of een custom icon
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/openingtimes-block.css' : '',
        'keywords'          => array('opening times', 'hours', 'schedule', 'business'),
    ));
    acf_register_block_type(array(
        'name'              => 'cta_block',
        'title'             => __('CTA Block'),
        'render_template'   => 'blocks/cta-block.php',
        'category'          => 'cta',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/cta-block.css' : '',
        'keywords'          => array('contact', 'cta', 'text', 'image'),
    ));
    acf_register_block_type(array(
        'name'              => 'sticky_date_text_block',
        'title'             => __('Sticky Date Text Block'),
        'render_template'   => 'blocks/sticky-date-text-block.php',
        'category'          => 'formatting',
        'icon'              => get_default_block_icon(),
        // 'enqueue_style'     => !is_admin() ? get_template_directory_uri() . '/blocks/css/sticky-date-text-block.css' : '',
        'enqueue_assets' => function(){
            if (!is_admin()) {
                wp_enqueue_script('sticky-date-text-block-js', get_template_directory_uri() . '/blocks/js/sticky-date-text-block.js', array('jquery'), time(), true);
            }
        },
        'supports'          => array(
            'jsx' => true,
            'align' => false,
            'anchor' => true,
            'mode' => false,
        ),
    ));

  }
}
function render_button($field_name) {
  $link = get_field($field_name);
  if( $link ) {
      $link_url = $link['url'];
      $link_title = $link['title'];
      $link_target = $link['target'] ? $link['target'] : '_self';
      echo '<a class="bigLink" href="' . esc_url( $link_url ) . '" title="'. esc_html( $link_title ) .'" target="' . esc_attr( $link_target ) . '"><span class="arrow"></span>' . esc_html( $link_title ) . '<span class="arrow"></span></a>';
  }
}
?>
