// out: false
* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
}

*:focus{
  outline:none;
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @hardWhite;
  color: @hardBlack;
  font-family: 'Lato', arial, sans-serif;
  overflow-x: hidden;
  font-size: @vw22;
  line-height: 1.4;
  .text {
    &.smaller {
      p {
        font-size: @vw16;
      }
    }
  }
  #cmplz-cookiebanner-container {
    display: none;
    opacity: 0;
  }
  > .flatpickr-calendar {
    display: none;
  }
  .cmplz-cookiebanner.cmplz-dismissed {
    display: none !important;
  }
  p {
    font-family: 'Lato', arial, sans-serif;
    font-size: @vw20;
    color: @hardBlack;
    font-weight: 400;
    line-height: 1.4;
    a {
      color: @pink;
      cursor: pointer;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
  a {
    cursor: pointer;
    text-decoration: none;
    * {
      cursor: pointer;
    }
  }
}

#pageContainer {
  background: @hardWhite;
  display: block;
  position: relative;
  z-index: 1;
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 + @vw50 0;
  &.noMarginTop {
    margin-top: 0;
  }
  &.noMarginBottom {
    margin-top: 0;
  }
  &.noBorderTop {
    &.black {
      &:before {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }
  &.noBorderBottom {
    &.black {
      &:before {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }
  &.dark {
    position: relative;
  }
  &.black {
    margin: 0;
    position: relative;
    padding: @vw100 + @vw50 0;
    &:before {
      content: '';
      border-radius: @vw20;
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
      background: @hardBlack;
    }
    .hugeTitle, .bigTitle, .normalTitle, .subTitle {
      color: @hardWhite;
    }
    .text {
      p {
        color: @grey;
      }
    }
  }
}

.contentWrapper {
  display: block;
  // width: @vw1474;
  width: 100vw;
  padding: 0 @vw104;
  margin: auto;
  &.small {
    padding: 0 (@vw112 * 2) + (@vw16 * 2) + @vw104;
  }
  &.smaller {
    padding: 0 @vw112 + @vw16 + @vw104;
  }
}

[data-split], [data-init] {
  // will-change: transform;
}

[data-split] {
  opacity: 0;
  &.inview {
    opacity: 1;
  }
}

.button {
  display: inline-block;
  text-decoration: none;
  font-family: 'The Light Font';
  font-size: @vw20;
  color: @hardBlack;
  line-height: @vw22;
  padding: @vw11 @vw22;
  position: relative;
  padding-bottom: @vw8;
  border-radius: @vw22;
  border: 1px solid @primaryColor;
  overflow: hidden;
  .transition(.3s);
  &:before {
    content: '';
    position: absolute;
    border-radius: 50%;
    height: 0;
    padding-bottom: 200%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    left: 1px;
    width: 200%;
    background: @primaryColor;
    .transition(.3s);
  }
  &.outline {
    color: @primaryColor;
  }
  &:not(.touch) {
    &:hover {
      transition-delay: .15s;
      &.outline {
        color: @hardWhite;
      }
      &.primary {
        color: @hardWhite;
        .innerTextWrapper {
          color: @hardWhite;
        }
      }
      &.black {
        border-color: @primaryColor;
      }
      &:before {
        transform: translate(-50%, -50%) scale(1);
        .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
      }
      .innerTextWrapper {
        .absoluteText {
          opacity: 0;
          transform: translateY(-100%);
          transition-delay: 0s;
          &:nth-child(2) {
            opacity: 1;
            transform: translateY(-50%);
            transition-delay: .15s;
          }
        }
      }
    }
  }
  i {
    position: relative;
    display: inline-block;
    vertical-align: middle;
  }
  .innerTextWrapper {
    display: inline-block;
    vertical-align: middle;
    padding-left: @vw5;
    toP: @vw3;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    .absoluteText {
      display: block;
      position: absolute;
      overflow: hidden;
      left: @vw5;
      top: 50%;
      transform: translateY(-50%);
      .transition(.3s);
      &:nth-child(2) {
        transform: translateY(0%);
        opacity: 0;
      }
    }
    .innerText {
      visibility: hidden;
    }
  }
  &.black {
    background: @hardBlack;
    color: @hardWhite;
    border-color: @hardBlack;
  }
}

.roundButton, .headerButton, .sliderButton{
  text-decoration: none;
  font-family: 'The Light Font';
  font-size: @vw22;
  width: @vw200;
  height: @vw200;
  color: @hardBlack;
  gap: @vw10;
  flex-direction: column;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  will-change: transform;
  .transitionMore(opacity, .3s, 0s, ease-in-out);
  &.disabled {
    pointer-events: none;
    opacity: .2;
  }
  &:hover {
    .innerText {
      i {
        padding-left: @vw10;
        padding-right: 0;
        transition-delay: .15s;
      }
    }
    .svgWrapper {
      svg {
        transform: scale(.9);
      }
    }
    .arrow {
      &:before {
        height: 80%;
      }
    }
  }
  .innerText {
    position: absolute;
    display: inline-block;
    span, i {
      display: block;
    }
    i {
      font-size: @vw50;
      .transition(.3s);
    }
  }
  .svgWrapper {
    position: absolute;
    animation: rotate360 20s linear infinite;
    width: 100%;
    height: 100%;
    svg {
      top: 0;
      left: 0;
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
  }
  .arrow {
    position: absolute;
    top: @vw20;
    left: 50%;
    transform: translateX(-50%);
    display: block;
    width: @vw15;
    height: calc(100% ~"-" @vw40);
    &:before {
      content: '';
      background: @hardWhite;
      height: 100%;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      position: absolute;
      width: 2px;
      .transition(.3s);
    }
    &:after {
      content: '';
      height: @vw15;
      width: @vw15;
      border-bottom: 2px solid @hardWhite;
      border-right: 2px solid @hardWhite;
      left: 50%;
      bottom: 2px;
      transform: translateX(-50%) rotate(45deg);
      position: absolute;
      .transition(.3s);
    }
  }
}

.headerButton, .sliderButton {
  cursor: pointer;
  height: @vw100;
  width: @vw100;
  * {
    cursor: pointer;
  }
}

.textLink {
  text-decoration: none;
  font-family: 'The Light Font';
  font-size: @vw22;
  color: @primaryColor;
  display: inline-block;
  &:hover {
    .innerText {
      padding-left: @vw10;
      padding-right: 0;
    }
    .arrow {
      &:before {
        width: 80%;
        transition-delay: .15s;
      }
    }
  }
  span {
    display: block;
    &:not(:last-child) {
      margin-bottom: @vw5;
    }
  }
  .innerText {
    .transition(.3s);
    padding-right: @vw10;
  }
  .arrow {
    display: block;
    width: 100%;
    height: @vw15;
    position: relative;
    margin: @vw10 auto 0 auto;
    &:before {
      content: '';
      background: @primaryColor;
      height: 2px;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      position: absolute;
      width: 100%;
      .transition(.3s);
    }
    &:after {
      content: '';
      height: @vw15;
      width: @vw15;
      border-top: 2px solid @primaryColor;
      border-right: 2px solid @primaryColor;
      right: 0;
      top: 50%;
      transform: translateY(-50%) rotate(45deg);
      position: absolute;
      .transition(.3s);
    }
  }
}

.imageItem {
  max-width: 100%;
  height: auto;
  position: absolute;
  overflow: hidden;

  &.inview {
    .innerImage {
      .innerMask {
        height: 100%;
        width: 100%;
        transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                    height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

        img {
          transform: scale(1);
          transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
        }
      }
    }
  }

  .innerImage {
    height: 0;
    padding-bottom: 100%;
    position: relative;  /* Added to ensure proper layout */

    .innerMask {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: @vw20;
      overflow: hidden;
      width: 0;
      height: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                  height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

      img {
        position: absolute;
        object-fit: cover;
        object-position: center;
        margin: auto;
        display: block;
        transform: scale(2);
        transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
        width: 100%;
        height: 100%;
      }
    }
  }
}


.socials {
  .social {
    color: @hardBlack;
    display: inline-block;
    text-decoration: none;
    padding: @vw8 @vw20;
    border-radius: @vw50;
    border: 1px solid @hardBlack;
    text-align: center;
    .transition(.3s);
    &:not(:last-child) {
      margin-right: @vw10;
    }
    &:hover {
      color: @primaryColor;
      background: @hardBlack;
    }
  }
}

.cmplz-body {
  p {
    font-size: @vw14;
  }
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 1160px) {
  body {
    font-size: @vw22-1160;
    .text {
      &.smaller {
        p {
          font-size: @vw16-1160;
        }
      }
    }
    p {
      font-size: @vw22-1160;
    }
  }

  section {
    margin: @vw100-1160 + @vw50-1160 0;
    &:first-child {
      padding-top: @vw100-1160;
    }
    &.black {
      padding: @vw100-1160 + @vw50-1160 0;
      &.first {
        padding-top: @vw100-1160 + @vw50-1160;
        &:before {
          border-radius: @vw20-1160 @vw20-1160 0 0;
        }
      }
      &:not(.last) {
        padding-bottom: 0;
      }
      &.last {
        &:before {
          border-radius: 0 0 @vw20-1160 @vw20-1160;
        }
      }
      &:before {
        // border-radius: @vw20-1160;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw50-1160;
    &.small {
      padding: 0 @vw112-1160;
    }
    &.smaller {
      padding: 0 @vw112-1160;
    }
  }

  .button {
    font-size: @vw20-1160;
    line-height: @vw22-1160;
    padding: @vw11-1160 @vw22-1160;
    padding-bottom: @vw8-1160;
    border-radius: @vw22-1160;
    &:before {
      top: 50%;
      left: @vw1-1160;
      padding-bottom: 200%;
    }
    .innerTextWrapper {
      padding-left: @vw5-1160;
      toP: @vw3-1160;
      .absoluteText {
        left: @vw5-1160;
      }
    }
  }
  .imageItem {
    .innerImage {
      .innerMask {
        border-radius: @vw20-1160;
      }
    }
  }

  .roundButton, .headerButton, .sliderButton {
    font-size: @vw22-1160;
    width: @vw100-1160 + @vw50-1160;
    height: @vw100-1160 + @vw50-1160;
    gap: @vw10-1160;
    .innerText {
      i {
        font-size: @vw30-1160;
      }
    }
    .arrow {
      height: calc(100% ~"-" @vw80-1160);
      top: @vw40-1160;
      width: @vw15-1160;
      &:after {
        height: @vw15-1160;
        width: @vw15-1160;
      }
    }
  }

  .sliderButton {
    width: @vw100-1160;
    height: @vw100-1160;
  }

  .textLink {
    font-size: @vw22-1160;
    span:not(:last-child) {
      margin-bottom: @vw5-1160;
    }
    .innerText {
      padding-right: @vw10-1160;
    }
    .arrow {
      height: @vw15-1160;
      margin: @vw10-1160 auto 0 auto;
      &:after {
        height: @vw15-1160;
        width: @vw15-1160;
      }
    }
  }

  .socials {
    .social {
      padding: @vw8-1160 @vw20-1160;
      border-radius: @vw50-1160;
      &:not(:last-child) {
        margin-right: @vw10-1160;
      }
    }
  }
  .cmplz-body {
    p {
      font-size: @vw14-1160;
    }
  }
}

@media screen and (max-width: 580px) {
  body {
    font-size: @vw22-580;
    .text {
      &.smaller {
        p {
          font-size: @vw22-580;
        }
      }
    }
    p {
      font-size: @vw22-580;
    }
  }

  section {
    margin: @vw100-580 + @vw50-580 0;
    &:first-child {
      padding-top: @vw100-580;
    }
    &.black {
      padding: @vw100-580 + @vw50-580 0;
      &:before {
        // border-radius: @vw20-580;
      }
      &.first {
        padding-top: @vw100-580 + @vw50-580;
        &:before {
          border-radius: @vw20-580 @vw20-580 0 0;
        }
      }
      &:not(.last) {
        padding-bottom: 0;
      }
      &.last {
        &:before {
          border-radius: 0 0 @vw20-580 @vw20-580;
        }
      }
    }
  }

  .imageItem {
    .innerImage {
      .innerMask {
        border-radius: @vw20-580;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw40-580;
    &.small {
      padding: 0 @vw40-580;
    }
    &.smaller {
      padding: 0 @vw40-580;
    }
  }

  .button {
    font-size: @vw20-580;
    line-height: @vw22-580;
    padding: @vw11-580 @vw22-580;
    padding-bottom: @vw8-580;
    border-radius: @vw22-580;
    &:before {
      top: 50%;
      left: @vw1-580;
      padding-bottom: 200%;
    }
    .innerTextWrapper {
      padding-left: @vw5-580;
      toP: @vw3-580;
      .absoluteText {
        left: @vw5-580;
      }
    }
  }

  .roundButton, .headerButton, .sliderButton {
    font-size: @vw22-580;
    width: @vw100-580 + @vw50-580;
    height: @vw100-580 + @vw50-580;
    gap: @vw10-580;
    .innerText {
      i {
        font-size: @vw30-580;
      }
    }
    .arrow {
      height: calc(100% ~"-" @vw80-580);
      top: @vw40-580;
      width: @vw15-580;
      &:after {
        height: @vw15-580;
        width: @vw15-580;
      }
    }
  }

  .roundButton {
    .innerText {
      top: @vw40-580;
    }
  }

  .sliderButton {
    width: @vw100-580;
    height: @vw100-580;
  }

  .textLink {
    font-size: @vw22-580;
    span:not(:last-child) {
      margin-bottom: @vw5-580;
    }
    .innerText {
      padding-right: @vw10-580;
    }
    .arrow {
      height: @vw15-580;
      margin: @vw10-580 auto 0 auto;
      &:after {
        height: @vw15-580;
        width: @vw15-580;
      }
    }
  }

  .socials {
    .social {
      padding: @vw8-580 @vw20-580;
      border-radius: @vw50-580;
      &:not(:last-child) {
        margin-right: @vw10-580;
      }
    }
  }
  .cmplz-body {
    p {
      font-size: @vw14-580;
    }
  }
}
