// out: false
.hugeTitle {
  color: @hardBlack;
  font-family: 'The Light Font';
  font-size: @vw100;
  font-weight: normal;
  line-height: 1;
  will-change: transform;
  span, strong {
    font-weight: normal;
    font-family: 'Local Brewery';
  }
  .wrapper {
    // padding-top: @vw5;
    position: relative;
    overflow: hidden;
    will-change: transform;
  }
  .word {
    will-change: transform;
  }
  &.white {
    color: @hardWhite;
  }
}

.bigTitle {
  color: @hardBlack;
  font-family: 'The Light Font';
  font-size: @vw70;
  font-weight: normal;
  line-height: 1;
  span, strong {
    font-weight: normal;
    font-family: 'Local Brewery';
  }
  .wrapper {
    padding-top: @vw5;
    display: inline !important;
    position: relative;
    overflow: hidden;
    will-change: transform;
  }
  .word {
    will-change: transform;
  }
  &.white {
    color: @hardWhite;
  }
}

.normalTitle {
  color: @hardBlack;
  font-family: 'The Light Font';
  font-size: @vw34;
  font-weight: normal;
  line-height: 1;
  span, strong {
    font-weight: normal;
    font-family: 'Local Brewery';
  }
  &.white {
    color: @hardWhite;
  }
  .wrapper {
    padding-top: @vw5;
    position: relative;
    overflow: hidden;
    will-change: transform;
  }
  .word {
    will-change: transform;
  }
}

.subTitle {
  color: @hardBlack;
  font-family: 'The Light Font';
  font-size: @vw22;
  font-weight: normal;
  line-height: 1;
  &.smaller {
    font-size: @vw16;
  }
  &.primary {
    color: @primaryColor !important;
  }
  .primary {
    color: @primaryColor;
  }
}

.text {
  &.grey {
    color: @grey;
    p {
      color: @grey;
    }
  }
  p {
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

.signature {
  color: @hardBlack;
  font-family: "Snell Roundhand";
  font-size: @vw22;
  font-weight: 100;
  line-height: 100%;
  &.primary {
    color: @primaryColor;
  }
}

@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: @vw100-1160;
    .wrapper {
      // padding-top: @vw5-1160;
    }
  }

  .bigTitle {
    font-size: @vw50-1160;
    .wrapper {
      padding-top: @vw5-1160;
    }
  }

  .normalTitle {
    font-size: @vw34-1160;
    .wrapper {
      padding-top: @vw5-1160;
    }
  }

  .subTitle {
    font-size: @vw22-1160;
    &.smaller {
      font-size: @vw16-1160;
    }
  }

  .text {
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1160;
      }
    }
  }

  .signature {
    font-size: @vw22-1160;
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw70-580;
    .wrapper {
      // padding-top: @vw5-580;
    }
  }

  .bigTitle {
    font-size: @vw42-580;
    span, strong {
      display: inline-block;
      font-size: @vw45-580;
    }
    .wrapper {
      padding-top: @vw5-580;
    }
  }

  .normalTitle {
    font-size: @vw34-580;
    .wrapper {
      padding-top: @vw5-580;
    }
  }

  .subTitle {
    font-size: @vw22-580;
    &.smaller {
      font-size: @vw22-580;
    }
  }

  .text {
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }

  .signature {
    font-size: @vw22-580;
  }
}
