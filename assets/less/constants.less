
// out: false
@primaryColor: #C39B08;
@primaryColorDark: #C39B08;
@secondaryColor: #ffc47b;
@secondaryColorLight: #FFF3E4;
@almostBlack: #232020;
@hardBlack: #232020;
@hardWhite: #FFFFFF;
@grey: #BDBCBC;
@lighterGrey;
@darkerGrey: #464646;
@lightGrey: #F0F0F0;
@borderGrey: #F5F5F5;
@almostWhite: #EDEDED;

@yellow: #D8A104;
@pink: #ED278F;
@green: #3FA745;
@blue: #3A2AF5;

@backgroundGrey: #F2F2F2;

// font sizes
@fsHuge: @vw100;
@fsBig: @vw52;
@fsNormal: @vw32;
@fsDefault: @vw21;

// line heights
@lhHuge: @vw100;
@lhBig: @vw63;
@lhNormal: @vw39;
@lhDefault: @vw32;

@maskAd: 1.2s;
.transitionLoopSplitter(@i, @transition) when (@i > 0) {
    &:nth-child(@{i}) {
        transition-delay: (@i * @transition);
    }
    .transitionLoopSplitter(@i - 1, @transition);
} 
.transition(@duration:0.2s, @ease:ease-out) {
    -webkit-transition: all @duration @ease;
    -moz-transition: all @duration @ease;
    -o-transition: all @duration @ease;
    transition: all @duration @ease;
}

.transitionMore(@what:all, @duration:0.2s, @delay:0s, @ease:ease-out) {
    -webkit-transition: @what @duration @delay @ease;
    -moz-transition: @what @duration @delay @ease;
    -o-transition: @what @duration @delay @ease;
    transition: @what @duration @delay @ease;
}

.padding-bottom-generator(@width, @height) {
    @padding-bottom: (@height / @width) * 100%;
    padding-bottom: @padding-bottom;
}
.transform(...) {
    -webkit-transform: @arguments;
    -moz-transform: @arguments;
    -o-transform: @arguments;
    -ms-transform: @arguments;
    transform: @arguments;
}