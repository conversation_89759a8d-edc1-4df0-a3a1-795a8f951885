// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Allemans
Author: <PERSON>
Version: 1.0.0
*/

@import 'vw_values.less'; 
@import 'constants.less';
@import 'default.less';
@import 'typo.less';
@import 'parts/header.less'; 
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/overlay.less';
@import 'parts/popup.less';
@import 'parts/sticky-whatsapp.less';
@import 'parts/ddsignature.less';

// blocks
@import '../../blocks/less/arrangementen-block.less';
@import '../../blocks/less/big-image-popup-block.less';
@import '../../blocks/less/contact-block.less';
@import '../../blocks/less/contact-details-block.less';
@import '../../blocks/less/cta-block.less';
@import '../../blocks/less/divider-block.less';
@import '../../blocks/less/gallery-block.less';
@import '../../blocks/less/header-block.less';
@import '../../blocks/less/home-about-block.less';
@import '../../blocks/less/home-header-block.less';
@import '../../blocks/less/home-intro-block.less';  
@import '../../blocks/less/image-text-block.less';
@import '../../blocks/less/images-grid-text-block.less';
@import '../../blocks/less/intro-text-block.less';
@import '../../blocks/less/menu-block.less'; 
@import '../../blocks/less/menu-highlight-block.less';
@import '../../blocks/less/menus-block.less';
@import '../../blocks/less/openingtimes-block.less'; 
@import '../../blocks/less/quote-block.less';
@import '../../blocks/less/quote-image-block.less';
@import '../../blocks/less/small-header-block.less';
@import '../../blocks/less/staff-slider-block.less';
@import '../../blocks/less/staff-block.less';
@import '../../blocks/less/sticky-date-text-block.less';
@import '../../blocks/less/two-col-text-block.less';

@font-face {
  font-family: 'Local Brewery';
  src:  url('assets/fonts/LocalBrewerySans.woff2') format('woff2'),
        url('assets/fonts/LocalBrewerySans.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'The Light Font';
  src:  url('assets/fonts/TheLightFont.woff2') format('woff2'),
        url('assets/fonts/TheLightFont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'Snell Roundhand';
  src:  url('assets/fonts/SnellRoundhand.woff2') format('woff2'),
        url('assets/fonts/SnellRoundhand.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'Lato';
  src:  url('assets/fonts/Lato-Regular.woff2') format('woff2'),
        url('assets/fonts/Lato-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?qwq3hi');
  src:  url('assets/fonts/icomoon.eot?qwq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?qwq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?qwq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?qwq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-bolwerk:before {
  content: "\e900";
}
.icon-download:before {
  content: "\e901";
}
.icon-facebook:before {
  content: "\e902";
}
.icon-food:before {
  content: "\e903";
}
.icon-instagram:before {
  content: "\e904";
}
.icon-location:before {
  content: "\e905";
}
.icon-long-arrow-down:before {
  content: "\e906";
}
.icon-long-arrow-left:before {
  content: "\e907";
}
.icon-long-arrow-right:before {
  content: "\e908";
}
.icon-long-arrow-top:before {
  content: "\e909";
}
.icon-phone:before {
  content: "\e90a";
}
.icon-view:before {
  content: "\e90b";
}
.icon-whatsapp:before {
  content: "\e90c";
}
.icon-tiktok:before {
  content: "\e90d";
}

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup
.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
  .cmplz-cookiebanner {
    opacity: 0;
    display: none;
  }
}

// Accessibility
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media all and (max-width: 1160px) {

}

@media all and (max-width: 580px) {

}
