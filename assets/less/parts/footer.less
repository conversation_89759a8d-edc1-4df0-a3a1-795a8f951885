// out: false
.whiteSpaceWrapper {
  pointer-events: none;
  z-index: -10;
}

.footer {
  padding-top: @vw100 + @vw50;
  padding-bottom: @vw30;
  background: @lightGrey;
  position: fixed;
  bottom: 0;
  width: 100%;
  min-height: 30vh;
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      margin: 0 @vw8;
      vertical-align: top;
      nav {
        ul {
          list-style: none;
        }
        li {
          a {
            color: @hardBlack;
            .transition(.3s);
            &:hover {
              opacity: .5;
            }
          }
        }
      }
    }
  }
  .topFooter {
    .col {
      width: calc(25% ~"-" @vw16);
    }
    nav {
      ul {
        li {
          &:not(:last-child) {
            margin-bottom: @vw16;
          }
        }
      }
    }
    .contactDetails {
      display: block;
      width: (@vw112 * 2) + @vw16;
      max-width: 100%;
      .links {
        margin: @vw30 0;
        .link {
          &:not(:last-child){
            margin-bottom: @vw10;
          }
          display: table;
          text-decoration: none;
          color: @hardBlack;
          cursor: pointer;
          .transition(.3s);
          &:hover {
            opacity: .7;
          }
          i {
            margin-right: @vw5;
          }
          span {
            text-decoration: underline;
          }
        }
      }
    }
    .socials {
      margin-top: @vw30;
    }
  }
  .bottomFooter {
    font-size: @vw16;
    padding-top: @vw80;
    .col {
      width: calc(25% ~"-" @vw16);
      vertical-align: middle;
      &:nth-child(2) { 
        width: calc(50% ~"-" @vw16);
      }
    }
    li {
      display: inline-block;
      vertical-align: middle;
    }
    .smallCol {
      display: inline-block;
      width: 50%;
      vertical-align: middle;
      &:nth-child(2) {
        text-align: right;
      }
    }
    .innerDivider {
      display: inline-block;
      margin: 0 @vw5;
      vertical-align: middle;
      opacity: .2;
    }
    .divider {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      .innerCol {
        display: inline-block;
        position: relative;
        &:nth-child(1), &:nth-child(3) {
          width: 100%;
          .innerBar {
            background: @hardBlack;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            width: 100%;
            height: 1px;
          }
        }
        &:nth-child(1) {
          .innerBar {
            right: 0;
            left: auto;
            -webkit-mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        &:nth-child(2) {
          text-align: center;
          width: @vw60 + @vw5;
        }
        &:nth-child(3) {
          .innerBar {
            -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        i {
          display: block;
          font-size: @vw16;
          color: @hardBlack;
        }
      }
    }
    .bigLogoWrapper {
      position: absolute;
      width: (@vw112 * 6) + (@vw16 * 5);
      max-width: 100%;
      top: 0;
      left: 0;
      right: 0;
      margin: auto;
      opacity: .05;
      animation: rotate360 60s linear infinite;
      z-index: -1;
      pointer-events: none;
      svg {
        width: 100%;
        height: 100%;
        object-fit: cover;
        path {
          fill: @hardBlack;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .footer {
    padding-top: @vw100-1160 + @vw50-1160;
    padding-bottom: @vw30-1160;
    .cols {
      margin-left: -@vw8-1160;
      width: calc(100% ~"+" @vw16-1160);
      .col {
        margin: 0 @vw8-1160;
        nav {
          ul {
            li {
              a {
                &:hover {
                  opacity: .5;
                }
              }
            }
          }
        }
      }
    }
    .topFooter {
      .col {
        width: calc(25% ~"-" @vw16-1160);
      }
      nav {
        ul {
          li {
            &:not(:last-child) {
              margin-bottom: @vw16-1160;
            }
          }
        }
      }
      .contactDetails {
        width: (@vw112-1160 * 2) + @vw16-1160;
        .links {
          margin: @vw30-1160 0;
          .link {
            &:not(:last-child) {
              margin-bottom: @vw10-1160;
            }
            i {
              margin-right: @vw5-1160;
            }
          }
        }
      }
      .socials {
        margin-top: @vw30-1160;
      }
    }
    .bottomFooter {
      font-size: @vw16-1160;
      padding-top: @vw80-1160;
      .col {
        width: calc(25% ~"-" @vw16-1160);
        &:nth-child(2) {
          width: calc(50% ~"-" @vw16-1160);
        }
      }
      .innerDivider {
        margin: 0 @vw5-1160;
      }
      .divider {
        .innerCol {
          &:nth-child(2) {
            width: @vw60-1160 + @vw5-1160;
          }
          i {
            font-size: @vw16-1160;
          }
        }
      }
      .bigLogoWrapper {
        width: (@vw112-1160 * 6) + (@vw16-1160 * 5);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .whiteSpaceWrapper {
    display: none;
  }
  .footer {
    position: relative;
    padding-top: @vw100-580 + @vw50-580;
    padding-bottom: @vw30-580;
    .cols {
      margin-bottom: -@vw30-580;
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        margin-bottom: @vw30-580;
        nav {
          ul {
            li {
              a {
                &:hover {
                  opacity: .5;
                }
              }
            }
          }
        }
      }
    }
    .topFooter {
      .col {
        width: calc(50% ~"-" @vw16-580);
      }
      nav {
        ul {
          li {
            &:not(:last-child) {
              margin-bottom: @vw16-580;
            }
          }
        }
      }
      .contactDetails {
        width: (@vw112-580 * 2) + @vw16-580;
        .links {
          margin: @vw30-580 0;
          .link {
            &:not(:last-child) {
              margin-bottom: @vw10-580;
            }
            i {
              margin-right: @vw5-580;
            }
          }
        }
      }
      .socials {
        margin-top: @vw30-580;
      }
    }
    .bottomFooter {
      font-size: @vw22-580;
      padding-top: @vw80-580;
      .col {
        text-align: center;
        width: calc(100% ~"-" @vw16-580);
        &:nth-child(2) {
          width: calc(100% ~"-" @vw16-580);
        }
      }
      .innerDivider {
        margin: 0 @vw5-580;
      }
      .divider {
        .innerCol {
          &:nth-child(2) {
            width: @vw60-580 + @vw5-580;
          }
          i {
            font-size: @vw16-580;
          }
        }
      }
      .bigLogoWrapper {
        top: -30%;
        z-index: 0;
        width: (@vw112-580 * 6) + (@vw16-580 * 5);
      }
    }
  }
}
