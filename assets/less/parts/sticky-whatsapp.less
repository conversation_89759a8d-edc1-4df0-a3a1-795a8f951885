// out: false

html {
  &:not(.touch) {
    .stickyWhatsapp {
      .stickyWhatsappButton {
        &:hover {
          transform: scale(1.05);
          .profileImageWrapper {
            box-shadow: 0 @vw4 @vw20 rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
}
.stickyWhatsapp {
  position: fixed;
  bottom: @vw50;
  left: @vw30;
  z-index: 8;

  .stickyWhatsappButton {
    display: block;
    position: relative;
    .transition(.3s);
    .availabilityIndicator {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      top: @vw5;
      right: 0;
      color: @hardWhite;
      line-height: @vw29;
      text-align: center;
      font-size: @vw14;
      width: @vw30; // 12px at base size
      height: @vw30; // 12px at base size
      border-radius: 50%;
      background-color: #25D366; // WhatsApp green
      border: 2px solid @hardWhite; // Fixed 2px border
      box-shadow: 0 @vw1 @vw3 rgba(0, 0, 0, 0.2);
    }

    .profileImageWrapper {
      border: 2px solid @hardWhite; // Fixed 2px border
      position: relative;
      display: block;
      width: @vw100;
      height: @vw100;
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 @vw2 @vw10 rgba(0, 0, 0, 0.1);
      .transition(.3s);

      .profileImage {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 1160px) {
  .stickyWhatsapp {
    bottom: @vw30-1160;
    left: @vw30-1160;

    .stickyWhatsappButton {
      .profileImageWrapper {
        width: @vw70-1160;
        height: @vw70-1160;
      }

      .profileImageWrapper {
        width: @vw100-1160;
        height: @vw100-1160;
        box-shadow: 0 @vw2-1160 @vw10-1160 rgba(0, 0, 0, 0.1);
      }

      .availabilityIndicator {
        right: @vw5-1160;
        width: @vw30-1160;
        height: @vw30-1160;
        border: 2px solid @hardWhite; // Keep fixed 2px border

        .icon-phone {
          font-size: @vw14-1160;
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .stickyWhatsapp {
    bottom: @vw20-580;
    left: @vw20-580;

    .stickyWhatsappButton {
      .profileImageWrapper {
        width: @vw60-580;
        height: @vw60-580;
      }

      .profileImageWrapper {
        width: @vw100-580;
        height: @vw100-580;
        box-shadow: 0 @vw2-580 @vw10-580 rgba(0, 0, 0, 0.1);
      }

      .availabilityIndicator {
        right: 0;
        width: @vw40-580;
        line-height: @vw40-580;
        height: @vw40-580;
        border: 2px solid @hardWhite; // Keep fixed 2px border
        .icon-phone {
          font-size: @vw16-580;
        }
      }
    }
  }
}
