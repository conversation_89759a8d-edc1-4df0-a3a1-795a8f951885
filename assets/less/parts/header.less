#header {
  position: fixed;
  padding-top: @vw20;
  top: 0;
  left: 0;
  z-index: 11;
  overflow: hidden;
  &.hide {
    transform: translateY(-100%);
    .transition(.3s);
  }
  &.scrolled {
    padding-bottom: @vw20;
    &:after {
      // opacity: 1;
    }
    .logo {
      img {
        opacity: 1;
        &.white {
          opacity: 0;
        }
      }
    }
    .hamburger {
      border-color: @hardBlack;
      .border {
        background: @hardBlack;
      }
    }
  }
  &.openMenu {
    .logo {
      img {
        opacity: 0;
        &.white {
          opacity: 1;
        }
      }
    }
    .hamburger {
      border-color: @hardBlack;
      .border {
        background: @hardBlack;
        &:nth-child(1) {
          transform: translateY(@vw5) rotate(45deg);
        }
        &:nth-child(2) {
          width: 0%;
        }
        &:nth-child(3) {
          transform: translateY(-@vw5) rotate(-45deg);
        }
      }
    }
  }
  &:after {
    content: '';
    background: @hardWhite;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: -1;
    .transition(.3s);
    border-bottom: 1px solid @lightGrey;
  }
  .col {
    display: inline-block;
    vertical-align: top;
    width: 50%;
    &:last-child {
      text-align: right;
      .button {
        text-align: left;
        &:not(:last-child) {
          margin-right: @vw16;
        }
      }
    }
  }
  .logo {
    height: @vw50;
    width: @vw100 + @vw50 + @vw5;
    display: block;
    position: relative;
    img {
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      height: 100%;
      width: 100%;
      object-fit: contain;
      .transition(.15s);
      &.white {
        opacity: 1;
      }
    }
  }
  .button {
    display: inline-block;
    vertical-align: middle;
  }
  .hamburger {
    cursor: pointer;
    height: @vw50;
    width: @vw50;
    border-radius: 50%;
    border: 1px solid @hardWhite;
    display: inline-flex;
    flex-direction: column;
    padding: @vw18 @vw14;
    align-items: center;
    vertical-align: middle;
    justify-content: space-between;
    * {
      cursor: pointer;
    }
    .border {
      background: @hardWhite;
      height: 2px;
      width: 100%;
      display: block;
      .transition(.3s);
    }
  }
}

#menu {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  pointer-events: none;
  overflow: hidden;
  opacity: 0;
  z-index: 10;
  .transition(.3s);
  transition-delay: 1.2s;
  &.openMenu {
    transition-delay: 0s;
    pointer-events: all;
    opacity: 1;
    .background {
      .col {
        height: 100%;
        transition-delay: 0s;
      }
      .bigLogoWrapper {
        svg {
          transform: scale(1);
          .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
        }
      }
    }
    .cols {
      .col {
        .innerCol {
          opacity: 1;
          transform: translateY(0);
          transition-delay: .45s;
          &:last-child {
            transition-delay: .9s;
          }
        }
      }
    }
  }
  .background {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    .col {
      position: absolute;
      height: 0%;
      width: 50%;
      .transition(.6s, cubic-bezier(0.85, 0, 0.15, 1));
      transition-delay: .45s;
      &:nth-child(1) {
        background: @hardBlack;
        left: 0;
        top: 0;
      }
      &:nth-child(2) {
        background: @primaryColor;
        right: 0;
        bottom: 0;
      }
    }
    .bigLogoWrapper {
      position: absolute;
      width: (@vw112 * 6) + (@vw16 * 5);
      max-width: 100%;
      opacity: .05;
      animation: rotate360 120s linear infinite;
      svg {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transform: scale(0);
        .transition(.3s);
        path {
          fill: @hardWhite;
        }
      }
    }
  }
  .contentWrapper {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  nav {
    a {
      color: @hardWhite;
      text-decoration: none;
      .transition(.3s);
      &:hover {
        opacity: .4;
      }
    }
  }
  .primaryMenu {
    li {
      &:not(:last-child) {
        margin-bottom: @vw22;
      }
    }
  }
  .secondaryMenu {
    li {
      &:not(:last-child) {
        margin-bottom: @vw12;
      }
    }
  }
  .cols {
    padding: @vw100 * 2 0;
    overflow-y: scroll;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: @vw40;
    .col {
      width: 100%;
      &.top {
        .innerCol {
          vertical-align: bottom;
        }
      }
      .innerCol {
        display: inline-block;
        vertical-align: top;
        width: 50%;
        opacity: 0;
        transform: translateY(@vw30);
        .transition(.45s);
        &.blend {
          mix-blend-mode: multiply;
        }
        &:nth-child(1) {
          padding-left: @vw112 + @vw16;
        }
        &:nth-child(2) {
          padding-left: @vw112 + (@vw16 * 2) + @vw8;
        }
      }
    }
    ul {
      list-style: none;
    }
  }
  img {
    display: inline-block;
    width: @vw112 + (@vw16 * 2);
    max-width: 100%;
    height: auto;
  }
  .contactDetails {
    display: block;
    width: (@vw112 * 2) + @vw16;
    max-width: 100%;
    .links {
      margin: @vw30 0;
      .link {
        &:not(:last-child){
          margin-bottom: @vw10;
        }
        display: table;
        text-decoration: none;
        color: @hardBlack;
        cursor: pointer;
        .transition(.3s);
        &:hover {
          opacity: .7;
        }
        i {
          margin-right: @vw5;
        }
        span {
          text-decoration: underline;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  #header {
    padding-top: @vw20-1160;
    &.scrolled {
      padding-bottom: @vw20-1160;
    }
    &.openMenu {
      .hamburger {
        .border {
          &:nth-child(1) {
            transform: translateY(@vw5-1160) rotate(45deg);
          }
          &:nth-child(3) {
            transform: translateY(-@vw5-1160) rotate(-45deg);
          }
        }
      }
    }
    .logo {
      height: @vw50-1160;
      width: calc(@vw100-1160 + @vw50-1160 + @vw5-1160);
    }
    .col {
      &:last-child {
        .button {
          &:not(:last-child) {
            margin-right: @vw16-1160;
          }
        }
      }
    }
    .hamburger {
      height: @vw50-1160;
      width: @vw50-1160;
      padding: calc(@vw18-1160) calc(@vw14-1160);
    }
  }

  #menu {
    .background {
      .bigLogoWrapper {
        width: calc(@vw112-1160 * 6 + @vw16-1160 * 5);
      }
    }
    .primaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw22-1160;
        }
      }
    }
    .secondaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw12-1160;
        }
      }
    }
    .cols {
      padding: @vw100-1160 + @vw40-1160 0;
      gap: @vw40-1160;
      .innerCol {
        transform: translateY(@vw30-1160);
        &:nth-child(1) {
          padding-left: calc(@vw112-1160 + @vw16-1160);
        }
        &:nth-child(2) {
          padding-left: calc(@vw112-1160 + @vw16-1160 * 2 + @vw8-1160);
        }
      }
    }
    img {
      width: calc(@vw112-1160 + @vw16-1160 * 2);
    }
    .contactDetails {
      width: calc(@vw112-1160 * 2 + @vw16-1160);
      .links {
        margin: calc(@vw30-1160) 0;
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-1160;
          }
          i {
            margin-right: @vw5-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  #header {
    padding-top: @vw20-580;
    &.scrolled {
      padding-bottom: @vw20-580;
    }
    &.openMenu {
      .button {
        &.primary {
          border-color: @hardWhite;
          color: @hardWhite;
        }
      }
      .hamburger {
        .border {
          &:nth-child(1) {
            transform: translateY(@vw5-580) rotate(45deg);
          }
          &:nth-child(3) {
            transform: translateY(-@vw5-580) rotate(-45deg);
          }
        }
      }
    }
  .logo {
    height: @vw50-580;
    width: calc(@vw100-580 + @vw50-580 + @vw5-580);
  }
  .col {
    .button {
      .innerTextWrapper {
        display: none;
      }
    }
    &:last-child {
      .button {
        &:not(:last-child) {
          margin-right: @vw16-580;
        }
      }
    }
  }
  .hamburger {
    height: @vw50-580;
    width: @vw50-580;
    padding: calc(@vw18-580) calc(@vw14-580);
  }
}

  #menu {
    .background {
      .bigLogoWrapper {
        width: calc(@vw112-580 * 6 + @vw16-580 * 5);
      }
    }
    .primaryMenu {
      font-size: @vw30-580;
      li {
        &:not(:last-child) {
          margin-bottom: @vw30-580;
        }
      }
    }
    .secondaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw22-580;
        }
      }
    }
    .cols {
      padding: @vw100-580 + @vw40-580 0;
      gap: @vw40-580;
      .col {
        &.top {
          vertical-align: top;
        }
        .innerCol {
          transform: translateY(@vw30-580);
          &:nth-child(1) {
            padding-left: 0;
          }
          &:nth-child(2) {
            padding-left: @vw22-580;
          }
        }
      }
    }
    img {
      width: calc(@vw112-580 + @vw16-580 * 2);
    }
    .contactDetails {
      width: calc(@vw112-580 * 2 + @vw16-580);
      .links {
        margin: calc(@vw30-580) 0;
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-580;
          }
          i {
            margin-right: @vw5-580;
          }
        }
      }
    }
  }
}
