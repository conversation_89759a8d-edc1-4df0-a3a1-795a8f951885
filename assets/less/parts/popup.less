// out: false
.sitePopup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  .transition(.3s);
  &.active {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
    .popupContent {
      transform: translate(-50%,-50%) scale(1);
      opacity: 1;
    }
    .popupOverlay {
      opacity: 1;
    }
  }

  .popupOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    cursor: pointer;
    opacity: 0;
    .transition(.3s);
  }

  .popupContent {
    position: absolute;
    top:50%;
    left: 50%;
    background: @hardWhite;
    border-radius: @vw20;
    max-width: 90%;
    width: (@vw112 * 8);
    max-height: 90vh;
    overflow-y: hidden;
    overflow-x: hidden;
    transform: translate(-50%,-50%) scale(.8);
    opacity: 0;
    .transition(.4s, cubic-bezier(0.175, 0.885, 0.32, 1.275));
  }

  .popupClose {
    position: absolute;
    top: @vw20;
    right: @vw20;
    z-index: 2;
    cursor: pointer;

    .closeIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: @vw40;
      height: @vw40;
      border-radius: 50%;
      background: @hardWhite;
      color: @hardBlack;
      font-size: @vw30;
      line-height: 1;
      .transition(.3s);
      &:hover {
        color: @primaryColor;
      }
    } 
  }

  .popupInner {
    display: flex;
    flex-direction: row;
  }

  .popupMedia {
    flex: 1;

    &.popupImage {
      img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;

        @media (min-width: 768px) {
          border-top-left-radius: @vw20;
          border-bottom-left-radius: @vw20;
        }
      }
    }

    &.popupVideo {
      position: relative;
      overflow: hidden;

      video, iframe {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;

        @media (min-width: 768px) {
          border-top-left-radius: @vw20;
          border-bottom-left-radius: @vw20;
        }
      }

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        min-height: 300px;
      }
    }
  }

  .popupText {
    flex: 1;
    padding: @vw50;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .bigTitle {
    margin-bottom: @vw20;
  }

  .popupDescription {
    margin-bottom: @vw30;

    p {
      margin-bottom: @vw16;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .popupButtons {
    display: flex;
    flex-wrap: wrap;
    gap: @vw16;
  }
}

// Responsive styles
@media screen and (max-width: 1160px) {
  .sitePopup {
    .popupContent {
      border-radius: @vw20-1160;
      width: (@vw112-1160 * 8);
    }

    .popupClose {
      top: @vw20-1160;
      right: @vw20-1160;

      .closeIcon {
        width: @vw40-1160;
        height: @vw40-1160;
        font-size: @vw30-1160;
      }
    }

    .popupMedia {
      &.popupImage, &.popupVideo {
        img, video {
          @media (min-width: 768px) {
            border-top-left-radius: @vw20-1160;
            border-bottom-left-radius: @vw20-1160;
          }
        }
      }
    }

    .popupText {
      padding: @vw50-1160;
    }

    .bigTitle {
      margin-bottom: @vw20-1160;
    }

    .popupDescription {
      margin-bottom: @vw30-1160;

      p {
        margin-bottom: @vw16-1160;
      }
    }

    .popupButtons {
      gap: @vw16-1160;

    }
  }
}

@media screen and (max-width: 580px) {
  .sitePopup {
    .popupContent {
      border-radius: @vw20-580;
      width: 90%;
      max-height: 80vh;
    }

    .popupClose {
      top: @vw20-580;
      right: @vw20-580;

      .closeIcon {
        width: @vw40-580;
        height: @vw40-580;
        font-size: @vw30-580;
      }
    }

    .popupMedia {
      &.popupImage, &.popupVideo {
        img, video {
          border-radius: 0;
          border-top-left-radius: @vw20-580;
          border-top-right-radius: @vw20-580;
        }
      }
    }

    .popupText {
      padding: @vw40-580;
    }

    .bigTitle {
      margin-bottom: @vw20-580;
    }

    .popupDescription {
      margin-bottom: @vw30-580;

      p {
        margin-bottom: @vw16-580;
      }
    }

    .popupButtons {
      gap: @vw16-580;

    }
  }
}
