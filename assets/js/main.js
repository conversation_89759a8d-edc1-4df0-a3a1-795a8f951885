var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

$(document).ready(function(){
    
    if ("ontouchstart" in document.documentElement){
        $("html").addClass("touch");
    }

    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }

    updateDynamicScriptsArray();
    
    document.addEventListener('wpcf7invalid', function (event) {
        setTimeout(function(){
            $('.wpcf7-form-control').each(function () {
                if ($(this).hasClass('wpcf7-not-valid')) {
                    $(this).closest(".field").addClass('invalid');
                } else { 
                    $(this).closest(".field").removeClass('invalid');
                }
            });
        }, 10);
    }, false);


    pageContainerWrap = new Swup({
        cache:true,
        containers: ["#pageContainer"],
        animateHistoryBrowsing: true,
        plugins: [new SwupHeadPlugin({
        persistAssets: true,
        ignoreVisit: (url, { el } = {}) => el?.closest('[data-no-swup]'),
        persistTags: 'style link', 
        }), new SwupGaPlugin({
            gaMeasurementId: 'G-EWXT780YLB',
        })]
    });
    
    
    $(document).off("click.hashtagLink", ".hashtagLink").on("click.hashtagLink", ".hashtagLink", function (e) {
        var hash = $(this).attr('href').replace('#', '').replace('/', '');
        if($("[data-anchor='" + hash + "']").length > 0){
            if(hash == "home"){
                scroller.scrollTo(0, {force:true}); 
            } else {
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {force:true}); 
            }
            history.pushState("", document.title, window.location.pathname + window.location.search);
        }
    });
    
    pageContainerWrap.hooks.on('link:click', () => {
        scrollValues[window.location.href] = window.scrollY;
    });

    pageContainerWrap.hooks.on('history:popstate', () => {
        popState = true;
        $(document).on("initPage", function(){
            if(popState){
                window.scrollTo(0, scrollValues[window.location.href]);
                popState = false;
            }
        });
    });
    
    containerWidth = $(window).width();
    
    preloadPage();
    
    pageContainerWrap.hooks.before('content:replace', () => {
        inlineStyles = $("head style");
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");
        //Store the inline styles from th head, so they can be put back when in pageView event
    });
    
    
    pageContainerWrap.hooks.on('page:view', () => {
        dynamicScriptLoad();
        updateDynamicScriptsArray();
        if (inlineStyles) {
            $("head").append($(inlineStyles));
            inlineStyles = null;
        }
        setTimeout(function(){
            initPage();
            
        }, 100);
    });
    
    pageContainerWrap.hooks.on('animation:out:end', () => {
        scroller.scrollTo(0,{offset: 0, duration:0, easing: "linear", immediate: true});
        $("header").removeClass("scrollDown");
        scroller.stop();
        scroller.start();
        $("html").addClass("stopScroll");
    });
    
    pageContainerWrap.hooks.before('content:replace', () => {
        $("html").addClass("stopScroll");
    });
    
    
});

function updateDynamicScriptsArray(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            dynamicScripts.push($(el).attr("src"));
        }
    });
}

function dynamicScriptLoad(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            let scriptEle = document.createElement("script");
            scriptEle.setAttribute("src", $(el).attr("src"));
            $(el).remove();
            document.head.appendChild(scriptEle);
        }
    });
    var container = $("#pageContainer")[0];
    var arr = container.getElementsByTagName('script');
    for (var n = 0; n < arr.length; n++){
        eval(arr[n].innerHTML);
    }
}

function initLenis(){
    scroller = new Lenis({
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
    });
    scroller.stop();
    scroller.on("scroll", function(e){
        $(document).trigger("scrollTrigger", currentScrollY);
    });
}

function raf(time) {
    scroller.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
}

function preloadPage(){
    initLenis();
    initPage();
    currentScrollY = $(window).scrollTop();
    scroller.on("scroll", function(e){
        currentScrollY = $(window).scrollTop();
    });

    requestAnimationFrame(raf);
    
    setTimeout(function(){
        $(".content").removeClass("fade");
        $("header").addClass("active");
    }, 300);
    
    
}

function initPage(){
    $("a[href*=\\#]").addClass("hashtagLink");
    $("html").removeClass("stopScroll fade");
    setTimeout(function(){
        if(window.location.hash){
            var hash = window.location.hash.replace('#', '').replace('/', '');
            if($("[data-anchor='" + hash + "']").length > 0){
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {lock: 1, force:1});
                history.pushState("", document.title, window.location.pathname + window.location.search);
            }
        }
        if($(".instagramBlock").length > 0){
            sbi_init(); 
        }
    }, 150);

    pageContainerWrap.hooks.on('page:view', () => {
        setTimeout(function(){
            if(window.location.hash){
                var hash = window.location.hash.replace('#', '').replace('/', '');
                if($("[data-anchor='" + hash + "']").length > 0){
                    scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {lock: 1, force:1});
                    history.pushState("", document.title, window.location.pathname + window.location.search);
                }
            }
        }, 150);
    });
    
    
    setTimeout(function(){
        $("html, body").removeClass("overflow");
        scroller.start();
        $(document).trigger("initPage"); 
        checkInviewClasses();
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");

        setTimeout(function() {
            if ($(".cmplz-optin").length > 0) {
                show_cookie_banner();
                console.log('show cookies');
                if ($(".cmplz-cookiebanner.cmplz-dismissed").length === 0) {
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").show();
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").css("opacity", "1");
                    $(".cmplz-optin").find(".cmplz-cookiebanner").show();
                    $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "1");
                }
            }
        }, 450);
    }, 600);
    

    lazyLoadImages();
    checkVideos();
    disableSwupOnReserverenLinks();

    if (!$(".contactBlock").length > 0) {
        $(".flatpickr-calendar").remove();
    }

    
}

function lazyLoadImages() {
    var lazyloadImages;
    if ("IntersectionObserver" in window) {
        lazyloadImages = document.querySelectorAll(".lazy");
        const config = {
            root: null, // avoiding 'root' or setting it to 'null' sets it to default value: viewport
            rootMargin: '500px',
            threshold: 0.0
        };
        var imageObserver = new IntersectionObserver(function (entries, observer) {
            $(entries).each(function (i, entry) {
                if (entry.isIntersecting) {
                    var image = entry.target;
                    image.classList.remove("lazy");
                    image.src = image.dataset.src;
                    imageObserver.unobserve(image);
                }
            });
        }, config);
        $(lazyloadImages).each(function (i, image) {
            imageObserver.observe(image);
        });
    } else {
        $(".lazy").each(function (i, image) {
            image.classList.remove("lazy");
            image.src = image.dataset.src;
        });
    }
}

function checkVideos() {
    const videos = document.querySelectorAll("video");
    videos.forEach(video => {
      ScrollTrigger.create({
        trigger: video,
        start: "top bottom",
        end: "bottom top",
        onEnter: () => video.play() ,
        onEnterBack: () => video.play(),
        onLeave: () => video.pause(),
        onLeaveBack: () => video.pause(),
        markers: false
      });
    });
  }

  function checkInviewClasses() {
    $("[data-init]").each(function() {
      const scrollDirect = $(this).data("scroll-direct");
      ScrollTrigger.create({
        trigger: this,
        start: scrollDirect ? "0% 100%" : "0% 90%",
        end: "0% 90%",
        once: true,
        onEnter: () => {
          if ($(this).get(0).hasAttribute("data-split") && !$(this).hasClass("inview")) {
            splitLines($(this));
          }
          if ($(this).get(0).hasAttribute("data-init-delay") && !$(this).hasClass("inview")) {
            var item = $(this);
            setTimeout(function() {
              $(item).addClass("inview");
            }, $(this).data("init-delay"));
          } else {
            $(this).addClass("inview");
          }
        }
      });
    });
  }
  
  function splitLines(splitThis) {
    var split = new SplitText(splitThis, { type: "words, lines", linesClass: "wrapper", wordsClass: "word" });
    var splitText = gsap.timeline();
    splitText.from(split.words, .45, {
      y: 50,
      autoAlpha: 0,
      stagger: 0.02,
    }).to(split.words, .45, {
      y: 0,
      autoAlpha: 1
    });
  }
  
  function splitButton(button) {
    var split = new SplitText(button, { type: "letters", lettersClass: "letter" });
    var splitText = gsap.timeline();
    splitText.from(split.letters, .9, {
      y: 100,
      autoAlpha: 0,
      stagger: 0.05,
    }).to(split.letters, .9, {
      y: 0,
      autoAlpha: 1
    });
  }

  function disableSwupOnReserverenLinks() {
    $('a[href*="reserveren"]').each(function () {
      $(this).attr('data-no-swup', 'true');
    });
  }