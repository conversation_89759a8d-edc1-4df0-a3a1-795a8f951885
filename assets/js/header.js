var menuIsOpen = false;
var menuDisabled = false;

$(document).ready(function(){
  $(document).on("initPage", function(){
    initMenu();
    checkSections();
    setTimeout(function() {
      setHeaderClass();
    }, 450);
  });
});

function initMenu() {
  gsap.registerPlugin(CustomEase);
  CustomEase.create("menubackground", "M0,0 C0.83,0 0.17,1 1,1");

  // Verwijder eerst oude event listeners om duplicaten te voorkomen
  $(document).off("click", "header .hamburger, #menu a");
  $(document).off("click", "header a");

  // Event listener voor de hamburger en menu links
  $(document).on("click", "header .hamburger, #menu a", function (e) {
    if (!menuDisabled) {
      menuDisabled = true;
      if (menuIsOpen) {
        closeMenu();
      } else {
        openMenu();
      }
    }
  });

  // Klik op een header link sluit het menu indien open
  $(document).on("click", "header a", function (e) {
    if (!menuDisabled && menuIsOpen) {
      closeMenu();
    }
  });

  // Zorg dat scrollen het menu sluit
  setTimeout(function () {
    scroller.on("scroll", function () {
      if (!menuDisabled && menuIsOpen) {
        closeMenu();
      }
    });
  }, 400);
}

function openMenu() {
  menuIsOpen = true;
  $("header, #menu").addClass("openMenu");
  scroller.stop();

  setTimeout(function () {
    $("header, #menu").find(".innerContent").addClass("showContent");
  }, 600);

  setTimeout(function () {
    menuDisabled = false;
  }, 900);
}

function closeMenu() {
  menuIsOpen = false;
  $("header, #menu").find(".innerContent").removeClass("showContent");

  setTimeout(function () {
    $("header, #menu").removeClass("openMenu");
  }, 600);

  setTimeout(function () {
    scroller.start();
    menuDisabled = false;
  }, 900);
}

function checkSections() {
  ScrollTrigger.getAll().forEach((trigger) => trigger.kill());

  const sections = document.querySelectorAll("section.black, section.dark");
  sections.forEach((section) => {
    ScrollTrigger.create({
      trigger: section,
      start: "top-=20 top",
      end: "bottom top",
      onEnter: () => $("header").removeClass("scrolled"),
      onEnterBack: () => $("header").removeClass("scrolled"),
      onLeave: () => $("header").addClass("scrolled"),
      onLeaveBack: () => $("header").addClass("scrolled"),
      markers: false,
    });
  });

  ScrollTrigger.refresh();
}

function setHeaderClass() {
  if (($('.headerBlock').length > 0)) {
    $("header").removeClass("scrolled");
  } else {
    $("header").addClass("scrolled");
  }
}